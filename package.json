{"name": "kubera-mythology-app", "version": "1.0.0", "description": "Kubera - A mythology-based horoscope and future reading app with dark theme", "main": "index.js", "scripts": {"dev": "cd web-app && npm run dev", "build": "cd web-app && npm run build", "preview": "cd web-app && npm run preview", "mobile:dev": "cd mobile-app && npm run dev", "mobile:build": "cd mobile-app && npm run build", "mobile:sync": "cd mobile-app && npx cap sync", "mobile:run:android": "cd mobile-app && npx cap run android", "mobile:run:ios": "cd mobile-app && npx cap run ios", "functions:dev": "cd firebase-functions && npm run serve", "functions:deploy": "cd firebase-functions && npm run deploy", "install:all": "npm install && cd web-app && npm install && cd ../mobile-app && npm install && cd ../firebase-functions && npm install"}, "keywords": ["mythology", "horoscope", "astrology", "kubera", "react", "capacitor", "firebase", "openai", "replicate"], "author": "Kubera Team", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0"}}