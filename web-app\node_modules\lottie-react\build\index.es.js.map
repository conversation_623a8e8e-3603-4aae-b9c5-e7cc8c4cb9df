{"version": 3, "file": "index.es.js", "sources": ["../compiled/hooks/useLottie.js", "../compiled/hooks/useLottieInteractivity.js", "../compiled/components/Lottie.js"], "sourcesContent": ["import lottie from \"lottie-web\";\nimport React, { useEffect, useRef, useState, } from \"react\";\nconst useLottie = (props, style) => {\n    const { animationData, loop, autoplay, initialSegment, onComplete, onLoopComplete, onEnterFrame, onSegmentStart, onConfigReady, onDataReady, onDataFailed, onLoadedImages, onDOMLoaded, onDestroy, \n    // Specified here to take them out from the 'rest'\n    lottieRef, renderer, name, assetsPath, rendererSettings, \n    // TODO: find a better way to extract the html props to avoid specifying\n    //  all the props that we want to exclude (as you can see above)\n    ...rest } = props;\n    const [animationLoaded, setAnimationLoaded] = useState(false);\n    const animationInstanceRef = useRef();\n    const animationContainer = useRef(null);\n    /*\n          ======================================\n              INTERACTION METHODS\n          ======================================\n       */\n    /**\n     * Play\n     */\n    const play = () => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.play();\n    };\n    /**\n     * Stop\n     */\n    const stop = () => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.stop();\n    };\n    /**\n     * Pause\n     */\n    const pause = () => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.pause();\n    };\n    /**\n     * Set animation speed\n     * @param speed\n     */\n    const setSpeed = (speed) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSpeed(speed);\n    };\n    /**\n     * Got to frame and play\n     * @param value\n     * @param isFrame\n     */\n    const goToAndPlay = (value, isFrame) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndPlay(value, isFrame);\n    };\n    /**\n     * Got to frame and stop\n     * @param value\n     * @param isFrame\n     */\n    const goToAndStop = (value, isFrame) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndStop(value, isFrame);\n    };\n    /**\n     * Set animation direction\n     * @param direction\n     */\n    const setDirection = (direction) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setDirection(direction);\n    };\n    /**\n     * Play animation segments\n     * @param segments\n     * @param forceFlag\n     */\n    const playSegments = (segments, forceFlag) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.playSegments(segments, forceFlag);\n    };\n    /**\n     * Set sub frames\n     * @param useSubFrames\n     */\n    const setSubframe = (useSubFrames) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSubframe(useSubFrames);\n    };\n    /**\n     * Get animation duration\n     * @param inFrames\n     */\n    const getDuration = (inFrames) => { var _a; return (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.getDuration(inFrames); };\n    /**\n     * Destroy animation\n     */\n    const destroy = () => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n        // Removing the reference to the animation so separate cleanups are skipped.\n        // Without it the internal `lottie-react` instance throws exceptions as it already cleared itself on destroy.\n        animationInstanceRef.current = undefined;\n    };\n    /*\n          ======================================\n              LOTTIE\n          ======================================\n       */\n    /**\n     * Load a new animation, and if it's the case, destroy the previous one\n     * @param {Object} forcedConfigs\n     */\n    const loadAnimation = (forcedConfigs = {}) => {\n        var _a;\n        // Return if the container ref is null\n        if (!animationContainer.current) {\n            return;\n        }\n        // Destroy any previous instance\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n        // Build the animation configuration\n        const config = {\n            ...props,\n            ...forcedConfigs,\n            container: animationContainer.current,\n        };\n        // Save the animation instance\n        animationInstanceRef.current = lottie.loadAnimation(config);\n        setAnimationLoaded(!!animationInstanceRef.current);\n        // Return a function that will clean up\n        return () => {\n            var _a;\n            (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n            animationInstanceRef.current = undefined;\n        };\n    };\n    /**\n     * (Re)Initialize when animation data changed\n     */\n    useEffect(() => {\n        const onUnmount = loadAnimation();\n        // Clean up on unmount\n        return () => onUnmount === null || onUnmount === void 0 ? void 0 : onUnmount();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [animationData, loop]);\n    // Update the autoplay state\n    useEffect(() => {\n        if (!animationInstanceRef.current) {\n            return;\n        }\n        animationInstanceRef.current.autoplay = !!autoplay;\n    }, [autoplay]);\n    // Update the initial segment state\n    useEffect(() => {\n        if (!animationInstanceRef.current) {\n            return;\n        }\n        // When null should reset to default animation length\n        if (!initialSegment) {\n            animationInstanceRef.current.resetSegments(true);\n            return;\n        }\n        // If it's not a valid segment, do nothing\n        if (!Array.isArray(initialSegment) || !initialSegment.length) {\n            return;\n        }\n        // If the current position it's not in the new segment\n        // set the current position to start\n        if (animationInstanceRef.current.currentRawFrame < initialSegment[0] ||\n            animationInstanceRef.current.currentRawFrame > initialSegment[1]) {\n            animationInstanceRef.current.currentRawFrame = initialSegment[0];\n        }\n        // Update the segment\n        animationInstanceRef.current.setSegment(initialSegment[0], initialSegment[1]);\n    }, [initialSegment]);\n    /*\n          ======================================\n              EVENTS\n          ======================================\n       */\n    /**\n     * Reinitialize listener on change\n     */\n    useEffect(() => {\n        const partialListeners = [\n            { name: \"complete\", handler: onComplete },\n            { name: \"loopComplete\", handler: onLoopComplete },\n            { name: \"enterFrame\", handler: onEnterFrame },\n            { name: \"segmentStart\", handler: onSegmentStart },\n            { name: \"config_ready\", handler: onConfigReady },\n            { name: \"data_ready\", handler: onDataReady },\n            { name: \"data_failed\", handler: onDataFailed },\n            { name: \"loaded_images\", handler: onLoadedImages },\n            { name: \"DOMLoaded\", handler: onDOMLoaded },\n            { name: \"destroy\", handler: onDestroy },\n        ];\n        const listeners = partialListeners.filter((listener) => listener.handler != null);\n        if (!listeners.length) {\n            return;\n        }\n        const deregisterList = listeners.map(\n        /**\n         * Handle the process of adding an event listener\n         * @param {Listener} listener\n         * @return {Function} Function that deregister the listener\n         */\n        (listener) => {\n            var _a;\n            (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.addEventListener(listener.name, listener.handler);\n            // Return a function to deregister this listener\n            return () => {\n                var _a;\n                (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(listener.name, listener.handler);\n            };\n        });\n        // Deregister listeners on unmount\n        return () => {\n            deregisterList.forEach((deregister) => deregister());\n        };\n    }, [\n        onComplete,\n        onLoopComplete,\n        onEnterFrame,\n        onSegmentStart,\n        onConfigReady,\n        onDataReady,\n        onDataFailed,\n        onLoadedImages,\n        onDOMLoaded,\n        onDestroy,\n    ]);\n    /**\n     * Build the animation view\n     */\n    const View = React.createElement(\"div\", { style: style, ref: animationContainer, ...rest });\n    return {\n        View,\n        play,\n        stop,\n        pause,\n        setSpeed,\n        goToAndStop,\n        goToAndPlay,\n        setDirection,\n        playSegments,\n        setSubframe,\n        getDuration,\n        destroy,\n        animationContainerRef: animationContainer,\n        animationLoaded,\n        animationItem: animationInstanceRef.current,\n    };\n};\nexport default useLottie;\n", "import { useEffect } from \"react\";\n// helpers\nexport function getContainerVisibility(container) {\n    const { top, height } = container.getBoundingClientRect();\n    const current = window.innerHeight - top;\n    const max = window.innerHeight + height;\n    return current / max;\n}\nexport function getContainerCursorPosition(container, cursorX, cursorY) {\n    const { top, left, width, height } = container.getBoundingClientRect();\n    const x = (cursorX - left) / width;\n    const y = (cursorY - top) / height;\n    return { x, y };\n}\nexport const useInitInteractivity = ({ wrapperRef, animationItem, mode, actions, }) => {\n    useEffect(() => {\n        const wrapper = wrapperRef.current;\n        if (!wrapper || !animationItem || !actions.length) {\n            return;\n        }\n        animationItem.stop();\n        const scrollModeHandler = () => {\n            let assignedSegment = null;\n            const scrollHandler = () => {\n                const currentPercent = getContainerVisibility(wrapper);\n                // Find the first action that satisfies the current position conditions\n                const action = actions.find(({ visibility }) => visibility &&\n                    currentPercent >= visibility[0] &&\n                    currentPercent <= visibility[1]);\n                // Skip if no matching action was found!\n                if (!action) {\n                    return;\n                }\n                if (action.type === \"seek\" &&\n                    action.visibility &&\n                    action.frames.length === 2) {\n                    // Seek: Go to a frame based on player scroll position action\n                    const frameToGo = action.frames[0] +\n                        Math.ceil(((currentPercent - action.visibility[0]) /\n                            (action.visibility[1] - action.visibility[0])) *\n                            action.frames[1]);\n                    //! goToAndStop must be relative to the start of the current segment\n                    animationItem.goToAndStop(frameToGo - animationItem.firstFrame - 1, true);\n                }\n                if (action.type === \"loop\") {\n                    // Loop: Loop a given frames\n                    if (assignedSegment === null) {\n                        // if not playing any segments currently. play those segments and save to state\n                        animationItem.playSegments(action.frames, true);\n                        assignedSegment = action.frames;\n                    }\n                    else {\n                        // if playing any segments currently.\n                        //check if segments in state are equal to the frames selected by action\n                        if (assignedSegment !== action.frames) {\n                            // if they are not equal. new segments are to be loaded\n                            animationItem.playSegments(action.frames, true);\n                            assignedSegment = action.frames;\n                        }\n                        else if (animationItem.isPaused) {\n                            // if they are equal the play method must be called only if lottie is paused\n                            animationItem.playSegments(action.frames, true);\n                            assignedSegment = action.frames;\n                        }\n                    }\n                }\n                if (action.type === \"play\" && animationItem.isPaused) {\n                    // Play: Reset segments and continue playing full animation from current position\n                    animationItem.resetSegments(true);\n                    animationItem.play();\n                }\n                if (action.type === \"stop\") {\n                    // Stop: Stop playback\n                    animationItem.goToAndStop(action.frames[0] - animationItem.firstFrame - 1, true);\n                }\n            };\n            document.addEventListener(\"scroll\", scrollHandler);\n            return () => {\n                document.removeEventListener(\"scroll\", scrollHandler);\n            };\n        };\n        const cursorModeHandler = () => {\n            const handleCursor = (_x, _y) => {\n                let x = _x;\n                let y = _y;\n                // Resolve cursor position if cursor is inside container\n                if (x !== -1 && y !== -1) {\n                    // Get container cursor position\n                    const pos = getContainerCursorPosition(wrapper, x, y);\n                    // Use the resolved position\n                    x = pos.x;\n                    y = pos.y;\n                }\n                // Find the first action that satisfies the current position conditions\n                const action = actions.find(({ position }) => {\n                    if (position &&\n                        Array.isArray(position.x) &&\n                        Array.isArray(position.y)) {\n                        return (x >= position.x[0] &&\n                            x <= position.x[1] &&\n                            y >= position.y[0] &&\n                            y <= position.y[1]);\n                    }\n                    if (position &&\n                        !Number.isNaN(position.x) &&\n                        !Number.isNaN(position.y)) {\n                        return x === position.x && y === position.y;\n                    }\n                    return false;\n                });\n                // Skip if no matching action was found!\n                if (!action) {\n                    return;\n                }\n                // Process action types:\n                if (action.type === \"seek\" &&\n                    action.position &&\n                    Array.isArray(action.position.x) &&\n                    Array.isArray(action.position.y) &&\n                    action.frames.length === 2) {\n                    // Seek: Go to a frame based on player scroll position action\n                    const xPercent = (x - action.position.x[0]) /\n                        (action.position.x[1] - action.position.x[0]);\n                    const yPercent = (y - action.position.y[0]) /\n                        (action.position.y[1] - action.position.y[0]);\n                    animationItem.playSegments(action.frames, true);\n                    animationItem.goToAndStop(Math.ceil(((xPercent + yPercent) / 2) *\n                        (action.frames[1] - action.frames[0])), true);\n                }\n                if (action.type === \"loop\") {\n                    animationItem.playSegments(action.frames, true);\n                }\n                if (action.type === \"play\") {\n                    // Play: Reset segments and continue playing full animation from current position\n                    if (animationItem.isPaused) {\n                        animationItem.resetSegments(false);\n                    }\n                    animationItem.playSegments(action.frames);\n                }\n                if (action.type === \"stop\") {\n                    animationItem.goToAndStop(action.frames[0], true);\n                }\n            };\n            const mouseMoveHandler = (ev) => {\n                handleCursor(ev.clientX, ev.clientY);\n            };\n            const mouseOutHandler = () => {\n                handleCursor(-1, -1);\n            };\n            wrapper.addEventListener(\"mousemove\", mouseMoveHandler);\n            wrapper.addEventListener(\"mouseout\", mouseOutHandler);\n            return () => {\n                wrapper.removeEventListener(\"mousemove\", mouseMoveHandler);\n                wrapper.removeEventListener(\"mouseout\", mouseOutHandler);\n            };\n        };\n        switch (mode) {\n            case \"scroll\":\n                return scrollModeHandler();\n            case \"cursor\":\n                return cursorModeHandler();\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [mode, animationItem]);\n};\nconst useLottieInteractivity = ({ actions, mode, lottieObj, }) => {\n    const { animationItem, View, animationContainerRef } = lottieObj;\n    useInitInteractivity({\n        actions,\n        animationItem,\n        mode,\n        wrapperRef: animationContainerRef,\n    });\n    return View;\n};\nexport default useLottieInteractivity;\n", "import { useEffect } from \"react\";\nimport useLottie from \"../hooks/useLottie\";\nimport useLottieInteractivity from \"../hooks/useLottieInteractivity\";\nconst Lottie = (props) => {\n    var _a, _b, _c;\n    const { style, interactivity, ...lottieProps } = props;\n    /**\n     * Initialize the 'useLottie' hook\n     */\n    const { View, play, stop, pause, setSpeed, goToAndStop, goToAndPlay, setDirection, playSegments, setSubframe, getDuration, destroy, animationContainerRef, animationLoaded, animationItem, } = useLottie(lottieProps, style);\n    /**\n     * Make the hook variables/methods available through the provided 'lottieRef'\n     */\n    useEffect(() => {\n        if (props.lottieRef) {\n            props.lottieRef.current = {\n                play,\n                stop,\n                pause,\n                setSpeed,\n                goToAndPlay,\n                goToAndStop,\n                setDirection,\n                playSegments,\n                setSubframe,\n                getDuration,\n                destroy,\n                animationContainerRef,\n                animationLoaded,\n                animationItem,\n            };\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [(_a = props.lottieRef) === null || _a === void 0 ? void 0 : _a.current]);\n    return useLottieInteractivity({\n        lottieObj: {\n            View,\n            play,\n            stop,\n            pause,\n            setSpeed,\n            goToAndStop,\n            goToAndPlay,\n            setDirection,\n            playSegments,\n            setSubframe,\n            getDuration,\n            destroy,\n            animationContainerRef,\n            animationLoaded,\n            animationItem,\n        },\n        actions: (_b = interactivity === null || interactivity === void 0 ? void 0 : interactivity.actions) !== null && _b !== void 0 ? _b : [],\n        mode: (_c = interactivity === null || interactivity === void 0 ? void 0 : interactivity.mode) !== null && _c !== void 0 ? _c : \"scroll\",\n    });\n};\nexport default Lottie;\n"], "names": ["useLottie", "props", "style", "animationData", "loop", "autoplay", "initialSegment", "onComplete", "onLoopComplete", "onEnterFrame", "onSegmentStart", "onConfigReady", "onDataReady", "onDataFailed", "onLoadedImages", "onDOMLoaded", "onDestroy", "lottieRef", "renderer", "name", "assetsPath", "rendererSettings", "rest", "_objectWithoutProperties", "_excluded", "_useState", "useState", "_useState2", "_slicedToArray", "animationLoaded", "setAnimationLoaded", "animationInstanceRef", "useRef", "animationContainer", "play", "_a", "current", "stop", "pause", "setSpeed", "speed", "goToAndPlay", "value", "isFrame", "goToAndStop", "setDirection", "direction", "playSegments", "segments", "forceFlag", "setSubframe", "useSubFrames", "getDuration", "inFrames", "destroy", "undefined", "loadAnimation", "forcedConfigs", "arguments", "length", "config", "_objectSpread", "container", "lottie", "useEffect", "onUnmount", "resetSegments", "Array", "isArray", "currentRawFrame", "setSegment", "partialListeners", "handler", "listeners", "filter", "listener", "deregisterList", "map", "addEventListener", "removeEventListener", "for<PERSON>ach", "deregister", "View", "React", "createElement", "ref", "animationContainerRef", "animationItem", "getContainerVisibility", "_container$getBoundin", "getBoundingClientRect", "top", "height", "window", "innerHeight", "max", "getContainerCursorPosition", "cursorX", "cursorY", "_container$getBoundin2", "left", "width", "x", "y", "useInitInteractivity", "_ref", "wrapperRef", "mode", "actions", "wrapper", "scrollModeHandler", "assignedSegment", "<PERSON><PERSON><PERSON><PERSON>", "currentPercent", "action", "find", "_ref2", "visibility", "type", "frames", "frameToGo", "Math", "ceil", "firstFrame", "isPaused", "document", "cursor<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleCursor", "_x", "_y", "pos", "_ref3", "position", "Number", "isNaN", "xPercent", "yPercent", "mouseMoveHandler", "ev", "clientX", "clientY", "mouseOutHandler", "useLottieInteractivity", "_ref4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "_b", "_c", "interactivity", "lottieProps", "_useL<PERSON>ie"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEMA,IAAAA,SAAS,GAAG,SAAZA,SAASA,CAAIC,KAAK,EAAEC,KAAK,EAAK;AAChC,EAAA,IAAQC,aAAa,GAKTF,KAAK,CALTE,aAAa,CAAA;IAAEC,IAAI,GAKfH,KAAK,CALMG,IAAI,CAAA;IAAEC,QAAQ,GAKzBJ,KAAK,CALYI,QAAQ,CAAA;IAAEC,cAAc,GAKzCL,KAAK,CALsBK,cAAc,CAAA;IAAEC,UAAU,GAKrDN,KAAK,CALsCM,UAAU,CAAA;IAAEC,cAAc,GAKrEP,KAAK,CALkDO,cAAc,CAAA;IAAEC,YAAY,GAKnFR,KAAK,CALkEQ,YAAY,CAAA;IAAEC,cAAc,GAKnGT,KAAK,CALgFS,cAAc,CAAA;IAAEC,aAAa,GAKlHV,KAAK,CALgGU,aAAa,CAAA;IAAEC,WAAW,GAK/HX,KAAK,CAL+GW,WAAW,CAAA;IAAEC,YAAY,GAK7IZ,KAAK,CAL4HY,YAAY,CAAA;IAAEC,cAAc,GAK7Jb,KAAK,CAL0Ia,cAAc,CAAA;IAAEC,WAAW,GAK1Kd,KAAK,CAL0Jc,WAAW,CAAA;IAAEC,SAAS,GAKrLf,KAAK,CALuKe,SAAS,CAAA;IAKrLf,KAAK,CAHjBgB,SAAS,CAAA;IAGGhB,KAAK,CAHNiB,QAAQ,CAAA;IAGPjB,KAAK,CAHIkB,IAAI,CAAA;IAGblB,KAAK,CAHUmB,UAAU,CAAA;IAGzBnB,KAAK,CAHsBoB,gBAAgB,CAAA;AAGpDC,QAAAA,IAAI,GAAAC,wBAAA,CAAKtB,KAAK,EAAAuB,WAAA,EAAA;AACjB,EAAA,IAAAC,SAAA,GAA8CC,QAAQ,CAAC,KAAK,CAAC;IAAAC,UAAA,GAAAC,cAAA,CAAAH,SAAA,EAAA,CAAA,CAAA;AAAtDI,IAAAA,eAAe,GAAAF,UAAA,CAAA,CAAA,CAAA;AAAEG,IAAAA,kBAAkB,GAAAH,UAAA,CAAA,CAAA,CAAA,CAAA;AAC1C,EAAA,IAAMI,oBAAoB,GAAGC,MAAM,EAAE,CAAA;AACrC,EAAA,IAAMC,kBAAkB,GAAGD,MAAM,CAAC,IAAI,CAAC,CAAA;AACvC;AACJ;AACA;AACA;AACA;AACI;AACJ;AACA;AACI,EAAA,IAAME,IAAI,GAAG,SAAPA,IAAIA,GAAS;AACf,IAAA,IAAIC,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,IAAI,EAAE,CAAA;GACrF,CAAA;AACD;AACJ;AACA;AACI,EAAA,IAAMG,IAAI,GAAG,SAAPA,IAAIA,GAAS;AACf,IAAA,IAAIF,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,EAAE,CAAA;GACrF,CAAA;AACD;AACJ;AACA;AACI,EAAA,IAAMC,KAAK,GAAG,SAARA,KAAKA,GAAS;AAChB,IAAA,IAAIH,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,KAAK,EAAE,CAAA;GACtF,CAAA;AACD;AACJ;AACA;AACA;AACI,EAAA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,KAAK,EAAK;AACxB,IAAA,IAAIL,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,CAACC,KAAK,CAAC,CAAA;GAC9F,CAAA;AACD;AACJ;AACA;AACA;AACA;EACI,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,KAAK,EAAEC,OAAO,EAAK;AACpC,IAAA,IAAIR,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC,CAAA;GAC1G,CAAA;AACD;AACJ;AACA;AACA;AACA;EACI,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIF,KAAK,EAAEC,OAAO,EAAK;AACpC,IAAA,IAAIR,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,WAAW,CAACF,KAAK,EAAEC,OAAO,CAAC,CAAA;GAC1G,CAAA;AACD;AACJ;AACA;AACA;AACI,EAAA,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAIC,SAAS,EAAK;AAChC,IAAA,IAAIX,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,YAAY,CAACC,SAAS,CAAC,CAAA;GACtG,CAAA;AACD;AACJ;AACA;AACA;AACA;EACI,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,QAAQ,EAAEC,SAAS,EAAK;AAC1C,IAAA,IAAId,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,YAAY,CAACC,QAAQ,EAAEC,SAAS,CAAC,CAAA;GAChH,CAAA;AACD;AACJ;AACA;AACA;AACI,EAAA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,YAAY,EAAK;AAClC,IAAA,IAAIhB,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,WAAW,CAACC,YAAY,CAAC,CAAA;GACxG,CAAA;AACD;AACJ;AACA;AACA;AACI,EAAA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,QAAQ,EAAK;AAAE,IAAA,IAAIlB,EAAE,CAAA;IAAE,OAAO,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,WAAW,CAACC,QAAQ,CAAC,CAAA;GAAG,CAAA;AACvJ;AACJ;AACA;AACI,EAAA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,GAAS;AAClB,IAAA,IAAInB,EAAE,CAAA;IACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,OAAO,EAAE,CAAA;AACrF;AACA;IACAvB,oBAAoB,CAACK,OAAO,GAAGmB,SAAS,CAAA;GAC3C,CAAA;AACD;AACJ;AACA;AACA;AACA;AACI;AACJ;AACA;AACA;AACI,EAAA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,GAA2B;AAAA,IAAA,IAAvBC,aAAa,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAH,SAAA,GAAAG,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;AACrC,IAAA,IAAIvB,EAAE,CAAA;AACN;AACA,IAAA,IAAI,CAACF,kBAAkB,CAACG,OAAO,EAAE;AAC7B,MAAA,OAAA;AACJ,KAAA;AACA;IACA,CAACD,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,OAAO,EAAE,CAAA;AACrF;IACA,IAAMM,MAAM,GAAAC,cAAA,CAAAA,cAAA,CAAAA,cAAA,CAAA,EAAA,EACL5D,KAAK,CAAA,EACLwD,aAAa,CAAA,EAAA,EAAA,EAAA;MAChBK,SAAS,EAAE7B,kBAAkB,CAACG,OAAAA;KACjC,CAAA,CAAA;AACD;IACAL,oBAAoB,CAACK,OAAO,GAAG2B,MAAM,CAACP,aAAa,CAACI,MAAM,CAAC,CAAA;AAC3D9B,IAAAA,kBAAkB,CAAC,CAAC,CAACC,oBAAoB,CAACK,OAAO,CAAC,CAAA;AAClD;AACA,IAAA,OAAO,YAAM;AACT,MAAA,IAAID,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,OAAO,EAAE,CAAA;MACrFvB,oBAAoB,CAACK,OAAO,GAAGmB,SAAS,CAAA;KAC3C,CAAA;GACJ,CAAA;AACD;AACJ;AACA;AACIS,EAAAA,SAAS,CAAC,YAAM;AACZ,IAAA,IAAMC,SAAS,GAAGT,aAAa,EAAE,CAAA;AACjC;IACA,OAAO,YAAA;AAAA,MAAA,OAAMS,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,EAAE,CAAA;AAAA,KAAA,CAAA;AAC9E;AACJ,GAAC,EAAE,CAAC9D,aAAa,EAAEC,IAAI,CAAC,CAAC,CAAA;AACzB;AACA4D,EAAAA,SAAS,CAAC,YAAM;AACZ,IAAA,IAAI,CAACjC,oBAAoB,CAACK,OAAO,EAAE;AAC/B,MAAA,OAAA;AACJ,KAAA;AACAL,IAAAA,oBAAoB,CAACK,OAAO,CAAC/B,QAAQ,GAAG,CAAC,CAACA,QAAQ,CAAA;AACtD,GAAC,EAAE,CAACA,QAAQ,CAAC,CAAC,CAAA;AACd;AACA2D,EAAAA,SAAS,CAAC,YAAM;AACZ,IAAA,IAAI,CAACjC,oBAAoB,CAACK,OAAO,EAAE;AAC/B,MAAA,OAAA;AACJ,KAAA;AACA;IACA,IAAI,CAAC9B,cAAc,EAAE;AACjByB,MAAAA,oBAAoB,CAACK,OAAO,CAAC8B,aAAa,CAAC,IAAI,CAAC,CAAA;AAChD,MAAA,OAAA;AACJ,KAAA;AACA;AACA,IAAA,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC9D,cAAc,CAAC,IAAI,CAACA,cAAc,CAACqD,MAAM,EAAE;AAC1D,MAAA,OAAA;AACJ,KAAA;AACA;AACA;IACA,IAAI5B,oBAAoB,CAACK,OAAO,CAACiC,eAAe,GAAG/D,cAAc,CAAC,CAAC,CAAC,IAChEyB,oBAAoB,CAACK,OAAO,CAACiC,eAAe,GAAG/D,cAAc,CAAC,CAAC,CAAC,EAAE;MAClEyB,oBAAoB,CAACK,OAAO,CAACiC,eAAe,GAAG/D,cAAc,CAAC,CAAC,CAAC,CAAA;AACpE,KAAA;AACA;AACAyB,IAAAA,oBAAoB,CAACK,OAAO,CAACkC,UAAU,CAAChE,cAAc,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA;AACjF,GAAC,EAAE,CAACA,cAAc,CAAC,CAAC,CAAA;AACpB;AACJ;AACA;AACA;AACA;AACI;AACJ;AACA;AACI0D,EAAAA,SAAS,CAAC,YAAM;IACZ,IAAMO,gBAAgB,GAAG,CACrB;AAAEpD,MAAAA,IAAI,EAAE,UAAU;AAAEqD,MAAAA,OAAO,EAAEjE,UAAAA;AAAW,KAAC,EACzC;AAAEY,MAAAA,IAAI,EAAE,cAAc;AAAEqD,MAAAA,OAAO,EAAEhE,cAAAA;AAAe,KAAC,EACjD;AAAEW,MAAAA,IAAI,EAAE,YAAY;AAAEqD,MAAAA,OAAO,EAAE/D,YAAAA;AAAa,KAAC,EAC7C;AAAEU,MAAAA,IAAI,EAAE,cAAc;AAAEqD,MAAAA,OAAO,EAAE9D,cAAAA;AAAe,KAAC,EACjD;AAAES,MAAAA,IAAI,EAAE,cAAc;AAAEqD,MAAAA,OAAO,EAAE7D,aAAAA;AAAc,KAAC,EAChD;AAAEQ,MAAAA,IAAI,EAAE,YAAY;AAAEqD,MAAAA,OAAO,EAAE5D,WAAAA;AAAY,KAAC,EAC5C;AAAEO,MAAAA,IAAI,EAAE,aAAa;AAAEqD,MAAAA,OAAO,EAAE3D,YAAAA;AAAa,KAAC,EAC9C;AAAEM,MAAAA,IAAI,EAAE,eAAe;AAAEqD,MAAAA,OAAO,EAAE1D,cAAAA;AAAe,KAAC,EAClD;AAAEK,MAAAA,IAAI,EAAE,WAAW;AAAEqD,MAAAA,OAAO,EAAEzD,WAAAA;AAAY,KAAC,EAC3C;AAAEI,MAAAA,IAAI,EAAE,SAAS;AAAEqD,MAAAA,OAAO,EAAExD,SAAAA;AAAU,KAAC,CAC1C,CAAA;AACD,IAAA,IAAMyD,SAAS,GAAGF,gBAAgB,CAACG,MAAM,CAAC,UAACC,QAAQ,EAAA;AAAA,MAAA,OAAKA,QAAQ,CAACH,OAAO,IAAI,IAAI,CAAA;KAAC,CAAA,CAAA;AACjF,IAAA,IAAI,CAACC,SAAS,CAACd,MAAM,EAAE;AACnB,MAAA,OAAA;AACJ,KAAA;AACA,IAAA,IAAMiB,cAAc,GAAGH,SAAS,CAACI,GAAG;AACpC;AACR;AACA;AACA;AACA;AACQ,IAAA,UAACF,QAAQ,EAAK;AACV,MAAA,IAAIxC,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2C,gBAAgB,CAACH,QAAQ,CAACxD,IAAI,EAAEwD,QAAQ,CAACH,OAAO,CAAC,CAAA;AAC7H;AACA,MAAA,OAAO,YAAM;AACT,QAAA,IAAIrC,EAAE,CAAA;QACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4C,mBAAmB,CAACJ,QAAQ,CAACxD,IAAI,EAAEwD,QAAQ,CAACH,OAAO,CAAC,CAAA;OACnI,CAAA;AACL,KAAC,CAAC,CAAA;AACF;AACA,IAAA,OAAO,YAAM;AACTI,MAAAA,cAAc,CAACI,OAAO,CAAC,UAACC,UAAU,EAAA;QAAA,OAAKA,UAAU,EAAE,CAAA;OAAC,CAAA,CAAA;KACvD,CAAA;GACJ,EAAE,CACC1E,UAAU,EACVC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,SAAS,CACZ,CAAC,CAAA;AACF;AACJ;AACA;EACI,IAAMkE,IAAI,gBAAGC,KAAK,CAACC,aAAa,CAAC,KAAK,EAAAvB,cAAA,CAAA;AAAI3D,IAAAA,KAAK,EAAEA,KAAK;AAAEmF,IAAAA,GAAG,EAAEpD,kBAAAA;GAAuBX,EAAAA,IAAI,CAAE,CAAC,CAAA;EAC3F,OAAO;AACH4D,IAAAA,IAAI,EAAJA,IAAI;AACJhD,IAAAA,IAAI,EAAJA,IAAI;AACJG,IAAAA,IAAI,EAAJA,IAAI;AACJC,IAAAA,KAAK,EAALA,KAAK;AACLC,IAAAA,QAAQ,EAARA,QAAQ;AACRK,IAAAA,WAAW,EAAXA,WAAW;AACXH,IAAAA,WAAW,EAAXA,WAAW;AACXI,IAAAA,YAAY,EAAZA,YAAY;AACZE,IAAAA,YAAY,EAAZA,YAAY;AACZG,IAAAA,WAAW,EAAXA,WAAW;AACXE,IAAAA,WAAW,EAAXA,WAAW;AACXE,IAAAA,OAAO,EAAPA,OAAO;AACPgC,IAAAA,qBAAqB,EAAErD,kBAAkB;AACzCJ,IAAAA,eAAe,EAAfA,eAAe;IACf0D,aAAa,EAAExD,oBAAoB,CAACK,OAAAA;GACvC,CAAA;AACL;;AC5PA;AACO,SAASoD,sBAAsBA,CAAC1B,SAAS,EAAE;AAC9C,EAAA,IAAA2B,qBAAA,GAAwB3B,SAAS,CAAC4B,qBAAqB,EAAE;IAAjDC,GAAG,GAAAF,qBAAA,CAAHE,GAAG;IAAEC,MAAM,GAAAH,qBAAA,CAANG,MAAM,CAAA;AACnB,EAAA,IAAMxD,OAAO,GAAGyD,MAAM,CAACC,WAAW,GAAGH,GAAG,CAAA;AACxC,EAAA,IAAMI,GAAG,GAAGF,MAAM,CAACC,WAAW,GAAGF,MAAM,CAAA;EACvC,OAAOxD,OAAO,GAAG2D,GAAG,CAAA;AACxB,CAAA;AACO,SAASC,0BAA0BA,CAAClC,SAAS,EAAEmC,OAAO,EAAEC,OAAO,EAAE;AACpE,EAAA,IAAAC,sBAAA,GAAqCrC,SAAS,CAAC4B,qBAAqB,EAAE;IAA9DC,GAAG,GAAAQ,sBAAA,CAAHR,GAAG;IAAES,IAAI,GAAAD,sBAAA,CAAJC,IAAI;IAAEC,KAAK,GAAAF,sBAAA,CAALE,KAAK;IAAET,MAAM,GAAAO,sBAAA,CAANP,MAAM,CAAA;AAChC,EAAA,IAAMU,CAAC,GAAG,CAACL,OAAO,GAAGG,IAAI,IAAIC,KAAK,CAAA;AAClC,EAAA,IAAME,CAAC,GAAG,CAACL,OAAO,GAAGP,GAAG,IAAIC,MAAM,CAAA;EAClC,OAAO;AAAEU,IAAAA,CAAC,EAADA,CAAC;AAAEC,IAAAA,CAAC,EAADA,CAAAA;GAAG,CAAA;AACnB,CAAA;AACO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAAC,IAAA,EAAsD;AAAA,EAAA,IAAhDC,UAAU,GAAAD,IAAA,CAAVC,UAAU;IAAEnB,aAAa,GAAAkB,IAAA,CAAblB,aAAa;IAAEoB,IAAI,GAAAF,IAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,IAAA,CAAPG,OAAO,CAAA;AAC3E5C,EAAAA,SAAS,CAAC,YAAM;AACZ,IAAA,IAAM6C,OAAO,GAAGH,UAAU,CAACtE,OAAO,CAAA;IAClC,IAAI,CAACyE,OAAO,IAAI,CAACtB,aAAa,IAAI,CAACqB,OAAO,CAACjD,MAAM,EAAE;AAC/C,MAAA,OAAA;AACJ,KAAA;IACA4B,aAAa,CAAClD,IAAI,EAAE,CAAA;AACpB,IAAA,IAAMyE,iBAAiB,GAAG,SAApBA,iBAAiBA,GAAS;MAC5B,IAAIC,eAAe,GAAG,IAAI,CAAA;AAC1B,MAAA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,GAAS;AACxB,QAAA,IAAMC,cAAc,GAAGzB,sBAAsB,CAACqB,OAAO,CAAC,CAAA;AACtD;AACA,QAAA,IAAMK,MAAM,GAAGN,OAAO,CAACO,IAAI,CAAC,UAAAC,KAAA,EAAA;AAAA,UAAA,IAAGC,UAAU,GAAAD,KAAA,CAAVC,UAAU,CAAA;AAAA,UAAA,OAAOA,UAAU,IACtDJ,cAAc,IAAII,UAAU,CAAC,CAAC,CAAC,IAC/BJ,cAAc,IAAII,UAAU,CAAC,CAAC,CAAC,CAAA;SAAC,CAAA,CAAA;AACpC;QACA,IAAI,CAACH,MAAM,EAAE;AACT,UAAA,OAAA;AACJ,SAAA;AACA,QAAA,IAAIA,MAAM,CAACI,IAAI,KAAK,MAAM,IACtBJ,MAAM,CAACG,UAAU,IACjBH,MAAM,CAACK,MAAM,CAAC5D,MAAM,KAAK,CAAC,EAAE;AAC5B;UACA,IAAM6D,SAAS,GAAGN,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,GAC9BE,IAAI,CAACC,IAAI,CAAE,CAACT,cAAc,GAAGC,MAAM,CAACG,UAAU,CAAC,CAAC,CAAC,KAC5CH,MAAM,CAACG,UAAU,CAAC,CAAC,CAAC,GAAGH,MAAM,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,GAC7CH,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;AACzB;AACAhC,UAAAA,aAAa,CAAC3C,WAAW,CAAC4E,SAAS,GAAGjC,aAAa,CAACoC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;AAC7E,SAAA;AACA,QAAA,IAAIT,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;AACxB;UACA,IAAIP,eAAe,KAAK,IAAI,EAAE;AAC1B;YACAxB,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;YAC/CR,eAAe,GAAGG,MAAM,CAACK,MAAM,CAAA;AACnC,WAAC,MACI;AACD;AACA;AACA,YAAA,IAAIR,eAAe,KAAKG,MAAM,CAACK,MAAM,EAAE;AACnC;cACAhC,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;cAC/CR,eAAe,GAAGG,MAAM,CAACK,MAAM,CAAA;AACnC,aAAC,MACI,IAAIhC,aAAa,CAACqC,QAAQ,EAAE;AAC7B;cACArC,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;cAC/CR,eAAe,GAAGG,MAAM,CAACK,MAAM,CAAA;AACnC,aAAA;AACJ,WAAA;AACJ,SAAA;QACA,IAAIL,MAAM,CAACI,IAAI,KAAK,MAAM,IAAI/B,aAAa,CAACqC,QAAQ,EAAE;AAClD;AACArC,UAAAA,aAAa,CAACrB,aAAa,CAAC,IAAI,CAAC,CAAA;UACjCqB,aAAa,CAACrD,IAAI,EAAE,CAAA;AACxB,SAAA;AACA,QAAA,IAAIgF,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;AACxB;AACA/B,UAAAA,aAAa,CAAC3C,WAAW,CAACsE,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,GAAGhC,aAAa,CAACoC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;AACpF,SAAA;OACH,CAAA;AACDE,MAAAA,QAAQ,CAAC/C,gBAAgB,CAAC,QAAQ,EAAEkC,aAAa,CAAC,CAAA;AAClD,MAAA,OAAO,YAAM;AACTa,QAAAA,QAAQ,CAAC9C,mBAAmB,CAAC,QAAQ,EAAEiC,aAAa,CAAC,CAAA;OACxD,CAAA;KACJ,CAAA;AACD,IAAA,IAAMc,iBAAiB,GAAG,SAApBA,iBAAiBA,GAAS;MAC5B,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,EAAE,EAAEC,EAAE,EAAK;QAC7B,IAAI3B,CAAC,GAAG0B,EAAE,CAAA;QACV,IAAIzB,CAAC,GAAG0B,EAAE,CAAA;AACV;QACA,IAAI3B,CAAC,KAAK,CAAC,CAAC,IAAIC,CAAC,KAAK,CAAC,CAAC,EAAE;AACtB;UACA,IAAM2B,GAAG,GAAGlC,0BAA0B,CAACa,OAAO,EAAEP,CAAC,EAAEC,CAAC,CAAC,CAAA;AACrD;UACAD,CAAC,GAAG4B,GAAG,CAAC5B,CAAC,CAAA;UACTC,CAAC,GAAG2B,GAAG,CAAC3B,CAAC,CAAA;AACb,SAAA;AACA;QACA,IAAMW,MAAM,GAAGN,OAAO,CAACO,IAAI,CAAC,UAAAgB,KAAA,EAAkB;AAAA,UAAA,IAAfC,QAAQ,GAAAD,KAAA,CAARC,QAAQ,CAAA;AACnC,UAAA,IAAIA,QAAQ,IACRjE,KAAK,CAACC,OAAO,CAACgE,QAAQ,CAAC9B,CAAC,CAAC,IACzBnC,KAAK,CAACC,OAAO,CAACgE,QAAQ,CAAC7B,CAAC,CAAC,EAAE;AAC3B,YAAA,OAAQD,CAAC,IAAI8B,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,IACtBA,CAAC,IAAI8B,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,IAClBC,CAAC,IAAI6B,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,IAClBA,CAAC,IAAI6B,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,CAAA;AAC1B,WAAA;UACA,IAAI6B,QAAQ,IACR,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC9B,CAAC,CAAC,IACzB,CAAC+B,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC7B,CAAC,CAAC,EAAE;YAC3B,OAAOD,CAAC,KAAK8B,QAAQ,CAAC9B,CAAC,IAAIC,CAAC,KAAK6B,QAAQ,CAAC7B,CAAC,CAAA;AAC/C,WAAA;AACA,UAAA,OAAO,KAAK,CAAA;AAChB,SAAC,CAAC,CAAA;AACF;QACA,IAAI,CAACW,MAAM,EAAE;AACT,UAAA,OAAA;AACJ,SAAA;AACA;AACA,QAAA,IAAIA,MAAM,CAACI,IAAI,KAAK,MAAM,IACtBJ,MAAM,CAACkB,QAAQ,IACfjE,KAAK,CAACC,OAAO,CAAC8C,MAAM,CAACkB,QAAQ,CAAC9B,CAAC,CAAC,IAChCnC,KAAK,CAACC,OAAO,CAAC8C,MAAM,CAACkB,QAAQ,CAAC7B,CAAC,CAAC,IAChCW,MAAM,CAACK,MAAM,CAAC5D,MAAM,KAAK,CAAC,EAAE;AAC5B;AACA,UAAA,IAAM4E,QAAQ,GAAG,CAACjC,CAAC,GAAGY,MAAM,CAACkB,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,KACrCY,MAAM,CAACkB,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,GAAGY,MAAM,CAACkB,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACjD,UAAA,IAAMkC,QAAQ,GAAG,CAACjC,CAAC,GAAGW,MAAM,CAACkB,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,KACrCW,MAAM,CAACkB,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,GAAGW,MAAM,CAACkB,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;UACjDhB,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;AAC/ChC,UAAAA,aAAa,CAAC3C,WAAW,CAAC6E,IAAI,CAACC,IAAI,CAAE,CAACa,QAAQ,GAAGC,QAAQ,IAAI,CAAC,IACzDtB,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,GAAGL,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AACrD,SAAA;AACA,QAAA,IAAIL,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;UACxB/B,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;AACnD,SAAA;AACA,QAAA,IAAIL,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;AACxB;UACA,IAAI/B,aAAa,CAACqC,QAAQ,EAAE;AACxBrC,YAAAA,aAAa,CAACrB,aAAa,CAAC,KAAK,CAAC,CAAA;AACtC,WAAA;AACAqB,UAAAA,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,CAAC,CAAA;AAC7C,SAAA;AACA,QAAA,IAAIL,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;UACxB/B,aAAa,CAAC3C,WAAW,CAACsE,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AACrD,SAAA;OACH,CAAA;AACD,MAAA,IAAMkB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,EAAE,EAAK;QAC7BX,YAAY,CAACW,EAAE,CAACC,OAAO,EAAED,EAAE,CAACE,OAAO,CAAC,CAAA;OACvC,CAAA;AACD,MAAA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,GAAS;AAC1Bd,QAAAA,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;OACvB,CAAA;AACDlB,MAAAA,OAAO,CAAC/B,gBAAgB,CAAC,WAAW,EAAE2D,gBAAgB,CAAC,CAAA;AACvD5B,MAAAA,OAAO,CAAC/B,gBAAgB,CAAC,UAAU,EAAE+D,eAAe,CAAC,CAAA;AACrD,MAAA,OAAO,YAAM;AACThC,QAAAA,OAAO,CAAC9B,mBAAmB,CAAC,WAAW,EAAE0D,gBAAgB,CAAC,CAAA;AAC1D5B,QAAAA,OAAO,CAAC9B,mBAAmB,CAAC,UAAU,EAAE8D,eAAe,CAAC,CAAA;OAC3D,CAAA;KACJ,CAAA;AACD,IAAA,QAAQlC,IAAI;AACR,MAAA,KAAK,QAAQ;QACT,OAAOG,iBAAiB,EAAE,CAAA;AAC9B,MAAA,KAAK,QAAQ;QACT,OAAOgB,iBAAiB,EAAE,CAAA;AAClC,KAAA;AACA;AACJ,GAAC,EAAE,CAACnB,IAAI,EAAEpB,aAAa,CAAC,CAAC,CAAA;AAC7B,CAAC,CAAA;AACD,IAAMuD,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAAC,KAAA,EAAsC;AAAA,EAAA,IAAhCnC,OAAO,GAAAmC,KAAA,CAAPnC,OAAO;IAAED,IAAI,GAAAoC,KAAA,CAAJpC,IAAI;IAAEqC,SAAS,GAAAD,KAAA,CAATC,SAAS,CAAA;AACtD,EAAA,IAAQzD,aAAa,GAAkCyD,SAAS,CAAxDzD,aAAa;IAAEL,IAAI,GAA4B8D,SAAS,CAAzC9D,IAAI;IAAEI,qBAAqB,GAAK0D,SAAS,CAAnC1D,qBAAqB,CAAA;AAClDkB,EAAAA,oBAAoB,CAAC;AACjBI,IAAAA,OAAO,EAAPA,OAAO;AACPrB,IAAAA,aAAa,EAAbA,aAAa;AACboB,IAAAA,IAAI,EAAJA,IAAI;AACJD,IAAAA,UAAU,EAAEpB,qBAAAA;AAChB,GAAC,CAAC,CAAA;AACF,EAAA,OAAOJ,IAAI,CAAA;AACf;;;AC3KA,IAAM+D,MAAM,GAAG,SAATA,MAAMA,CAAIhJ,KAAK,EAAK;AACtB,EAAA,IAAIkC,EAAE,EAAE+G,EAAE,EAAEC,EAAE,CAAA;AACd,EAAA,IAAQjJ,KAAK,GAAoCD,KAAK,CAA9CC,KAAK;IAAEkJ,aAAa,GAAqBnJ,KAAK,CAAvCmJ,aAAa;AAAKC,IAAAA,WAAW,GAAA9H,wBAAA,CAAKtB,KAAK,EAAAuB,SAAA,CAAA,CAAA;AACtD;AACJ;AACA;AACI,EAAA,IAAA8H,UAAA,GAA+LtJ,SAAS,CAACqJ,WAAW,EAAEnJ,KAAK,CAAC;IAApNgF,IAAI,GAAAoE,UAAA,CAAJpE,IAAI;IAAEhD,IAAI,GAAAoH,UAAA,CAAJpH,IAAI;IAAEG,IAAI,GAAAiH,UAAA,CAAJjH,IAAI;IAAEC,KAAK,GAAAgH,UAAA,CAALhH,KAAK;IAAEC,QAAQ,GAAA+G,UAAA,CAAR/G,QAAQ;IAAEK,WAAW,GAAA0G,UAAA,CAAX1G,WAAW;IAAEH,WAAW,GAAA6G,UAAA,CAAX7G,WAAW;IAAEI,YAAY,GAAAyG,UAAA,CAAZzG,YAAY;IAAEE,YAAY,GAAAuG,UAAA,CAAZvG,YAAY;IAAEG,WAAW,GAAAoG,UAAA,CAAXpG,WAAW;IAAEE,WAAW,GAAAkG,UAAA,CAAXlG,WAAW;IAAEE,OAAO,GAAAgG,UAAA,CAAPhG,OAAO;IAAEgC,qBAAqB,GAAAgE,UAAA,CAArBhE,qBAAqB;IAAEzD,eAAe,GAAAyH,UAAA,CAAfzH,eAAe;IAAE0D,aAAa,GAAA+D,UAAA,CAAb/D,aAAa,CAAA;AACzL;AACJ;AACA;AACIvB,EAAAA,SAAS,CAAC,YAAM;IACZ,IAAI/D,KAAK,CAACgB,SAAS,EAAE;AACjBhB,MAAAA,KAAK,CAACgB,SAAS,CAACmB,OAAO,GAAG;AACtBF,QAAAA,IAAI,EAAJA,IAAI;AACJG,QAAAA,IAAI,EAAJA,IAAI;AACJC,QAAAA,KAAK,EAALA,KAAK;AACLC,QAAAA,QAAQ,EAARA,QAAQ;AACRE,QAAAA,WAAW,EAAXA,WAAW;AACXG,QAAAA,WAAW,EAAXA,WAAW;AACXC,QAAAA,YAAY,EAAZA,YAAY;AACZE,QAAAA,YAAY,EAAZA,YAAY;AACZG,QAAAA,WAAW,EAAXA,WAAW;AACXE,QAAAA,WAAW,EAAXA,WAAW;AACXE,QAAAA,OAAO,EAAPA,OAAO;AACPgC,QAAAA,qBAAqB,EAArBA,qBAAqB;AACrBzD,QAAAA,eAAe,EAAfA,eAAe;AACf0D,QAAAA,aAAa,EAAbA,aAAAA;OACH,CAAA;AACL,KAAA;AACA;GACH,EAAE,CAAC,CAACpD,EAAE,GAAGlC,KAAK,CAACgB,SAAS,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,OAAO,CAAC,CAAC,CAAA;AAC5E,EAAA,OAAO0G,sBAAsB,CAAC;AAC1BE,IAAAA,SAAS,EAAE;AACP9D,MAAAA,IAAI,EAAJA,IAAI;AACJhD,MAAAA,IAAI,EAAJA,IAAI;AACJG,MAAAA,IAAI,EAAJA,IAAI;AACJC,MAAAA,KAAK,EAALA,KAAK;AACLC,MAAAA,QAAQ,EAARA,QAAQ;AACRK,MAAAA,WAAW,EAAXA,WAAW;AACXH,MAAAA,WAAW,EAAXA,WAAW;AACXI,MAAAA,YAAY,EAAZA,YAAY;AACZE,MAAAA,YAAY,EAAZA,YAAY;AACZG,MAAAA,WAAW,EAAXA,WAAW;AACXE,MAAAA,WAAW,EAAXA,WAAW;AACXE,MAAAA,OAAO,EAAPA,OAAO;AACPgC,MAAAA,qBAAqB,EAArBA,qBAAqB;AACrBzD,MAAAA,eAAe,EAAfA,eAAe;AACf0D,MAAAA,aAAa,EAAbA,aAAAA;KACH;AACDqB,IAAAA,OAAO,EAAE,CAACsC,EAAE,GAAGE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACxC,OAAO,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;AACvIvC,IAAAA,IAAI,EAAE,CAACwC,EAAE,GAAGC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACzC,IAAI,MAAM,IAAI,IAAIwC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,QAAA;AACnI,GAAC,CAAC,CAAA;AACN;;;;"}