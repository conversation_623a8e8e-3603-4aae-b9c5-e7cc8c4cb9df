import e from"lottie-web";export{default as LottiePlayer}from"lottie-web";import t,{useState as n,useRef as r,useEffect as o}from"react";function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function a(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){a(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],l=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(e,t)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var f=["animationData","loop","autoplay","initialSegment","onComplete","onLoopComplete","onEnterFrame","onSegmentStart","onConfigReady","onDataReady","onDataFailed","onLoadedImages","onDOMLoaded","onDestroy","lottieRef","renderer","name","assetsPath","rendererSettings"],d=function(i,a){var u=i.animationData,d=i.loop,m=i.autoplay,p=i.initialSegment,y=i.onComplete,v=i.onLoopComplete,g=i.onEnterFrame,b=i.onSegmentStart,S=i.onConfigReady,h=i.onDataReady,w=i.onDataFailed,A=i.onLoadedImages,O=i.onDOMLoaded,D=i.onDestroy;i.lottieRef,i.renderer,i.name,i.assetsPath,i.rendererSettings;var P=s(i,f),j=c(n(!1),2),R=j[0],L=j[1],E=r(),T=r(null);return o((function(){var t=function(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(T.current){null===(t=E.current)||void 0===t||t.destroy();var r=l(l(l({},i),n),{},{container:T.current});return E.current=e.loadAnimation(r),L(!!E.current),function(){var e;null===(e=E.current)||void 0===e||e.destroy(),E.current=void 0}}}();return function(){return null==t?void 0:t()}}),[u,d]),o((function(){E.current&&(E.current.autoplay=!!m)}),[m]),o((function(){E.current&&(p?Array.isArray(p)&&p.length&&((E.current.currentRawFrame<p[0]||E.current.currentRawFrame>p[1])&&(E.current.currentRawFrame=p[0]),E.current.setSegment(p[0],p[1])):E.current.resetSegments(!0))}),[p]),o((function(){var e=[{name:"complete",handler:y},{name:"loopComplete",handler:v},{name:"enterFrame",handler:g},{name:"segmentStart",handler:b},{name:"config_ready",handler:S},{name:"data_ready",handler:h},{name:"data_failed",handler:w},{name:"loaded_images",handler:A},{name:"DOMLoaded",handler:O},{name:"destroy",handler:D}].filter((function(e){return null!=e.handler}));if(e.length){var t=e.map((function(e){var t;return null===(t=E.current)||void 0===t||t.addEventListener(e.name,e.handler),function(){var t;null===(t=E.current)||void 0===t||t.removeEventListener(e.name,e.handler)}}));return function(){t.forEach((function(e){return e()}))}}}),[y,v,g,b,S,h,w,A,O,D]),{View:t.createElement("div",l({style:a,ref:T},P)),play:function(){var e;null===(e=E.current)||void 0===e||e.play()},stop:function(){var e;null===(e=E.current)||void 0===e||e.stop()},pause:function(){var e;null===(e=E.current)||void 0===e||e.pause()},setSpeed:function(e){var t;null===(t=E.current)||void 0===t||t.setSpeed(e)},goToAndStop:function(e,t){var n;null===(n=E.current)||void 0===n||n.goToAndStop(e,t)},goToAndPlay:function(e,t){var n;null===(n=E.current)||void 0===n||n.goToAndPlay(e,t)},setDirection:function(e){var t;null===(t=E.current)||void 0===t||t.setDirection(e)},playSegments:function(e,t){var n;null===(n=E.current)||void 0===n||n.playSegments(e,t)},setSubframe:function(e){var t;null===(t=E.current)||void 0===t||t.setSubframe(e)},getDuration:function(e){var t;return null===(t=E.current)||void 0===t?void 0:t.getDuration(e)},destroy:function(){var e;null===(e=E.current)||void 0===e||e.destroy(),E.current=void 0},animationContainerRef:T,animationLoaded:R,animationItem:E.current}};var m=function(e){var t=e.wrapperRef,n=e.animationItem,r=e.mode,i=e.actions;o((function(){var e=t.current;if(e&&n&&i.length){n.stop();var o,a,u,l,s;switch(r){case"scroll":return l=null,s=function(){var t,r,o,a=(r=(t=e.getBoundingClientRect()).top,o=t.height,(window.innerHeight-r)/(window.innerHeight+o)),u=i.find((function(e){var t=e.visibility;return t&&a>=t[0]&&a<=t[1]}));if(u){if("seek"===u.type&&u.visibility&&2===u.frames.length){var s=u.frames[0]+Math.ceil((a-u.visibility[0])/(u.visibility[1]-u.visibility[0])*u.frames[1]);
//! goToAndStop must be relative to the start of the current segment
n.goToAndStop(s-n.firstFrame-1,!0)}"loop"===u.type&&(null===l||l!==u.frames||n.isPaused)&&(n.playSegments(u.frames,!0),l=u.frames),"play"===u.type&&n.isPaused&&(n.resetSegments(!0),n.play()),"stop"===u.type&&n.goToAndStop(u.frames[0]-n.firstFrame-1,!0)}},document.addEventListener("scroll",s),function(){document.removeEventListener("scroll",s)};case"cursor":return o=function(t,r){var o,a,u,l,s=t,c=r;if(-1!==s&&-1!==c){var f=(o=s,a=c,l=(u=e.getBoundingClientRect()).top,{x:(o-u.left)/u.width,y:(a-l)/u.height});s=f.x,c=f.y}var d=i.find((function(e){var t=e.position;return t&&Array.isArray(t.x)&&Array.isArray(t.y)?s>=t.x[0]&&s<=t.x[1]&&c>=t.y[0]&&c<=t.y[1]:!(!t||Number.isNaN(t.x)||Number.isNaN(t.y))&&s===t.x&&c===t.y}));if(d){if("seek"===d.type&&d.position&&Array.isArray(d.position.x)&&Array.isArray(d.position.y)&&2===d.frames.length){var m=(s-d.position.x[0])/(d.position.x[1]-d.position.x[0]),p=(c-d.position.y[0])/(d.position.y[1]-d.position.y[0]);n.playSegments(d.frames,!0),n.goToAndStop(Math.ceil((m+p)/2*(d.frames[1]-d.frames[0])),!0)}"loop"===d.type&&n.playSegments(d.frames,!0),"play"===d.type&&(n.isPaused&&n.resetSegments(!1),n.playSegments(d.frames)),"stop"===d.type&&n.goToAndStop(d.frames[0],!0)}},a=function(e){o(e.clientX,e.clientY)},u=function(){o(-1,-1)},e.addEventListener("mousemove",a),e.addEventListener("mouseout",u),function(){e.removeEventListener("mousemove",a),e.removeEventListener("mouseout",u)}}}}),[r,n])},p=function(e){var t=e.actions,n=e.mode,r=e.lottieObj,o=r.animationItem,i=r.View,a=r.animationContainerRef;return m({actions:t,animationItem:o,mode:n,wrapperRef:a}),i},y=["style","interactivity"],v=function(e){var t,n,r,i=e.style,a=e.interactivity,u=s(e,y),l=d(u,i),c=l.View,f=l.play,m=l.stop,v=l.pause,g=l.setSpeed,b=l.goToAndStop,S=l.goToAndPlay,h=l.setDirection,w=l.playSegments,A=l.setSubframe,O=l.getDuration,D=l.destroy,P=l.animationContainerRef,j=l.animationLoaded,R=l.animationItem;return o((function(){e.lottieRef&&(e.lottieRef.current={play:f,stop:m,pause:v,setSpeed:g,goToAndPlay:S,goToAndStop:b,setDirection:h,playSegments:w,setSubframe:A,getDuration:O,destroy:D,animationContainerRef:P,animationLoaded:j,animationItem:R})}),[null===(t=e.lottieRef)||void 0===t?void 0:t.current]),p({lottieObj:{View:c,play:f,stop:m,pause:v,setSpeed:g,goToAndStop:b,goToAndPlay:S,setDirection:h,playSegments:w,setSubframe:A,getDuration:O,destroy:D,animationContainerRef:P,animationLoaded:j,animationItem:R},actions:null!==(n=null==a?void 0:a.actions)&&void 0!==n?n:[],mode:null!==(r=null==a?void 0:a.mode)&&void 0!==r?r:"scroll"})};export{v as default,d as useLottie,p as useLottieInteractivity};
