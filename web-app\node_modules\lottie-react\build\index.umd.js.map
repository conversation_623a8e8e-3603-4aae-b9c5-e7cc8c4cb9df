{"version": 3, "file": "index.umd.js", "sources": ["../compiled/hooks/useLottie.js", "../compiled/hooks/useLottieInteractivity.js", "../compiled/components/Lottie.js"], "sourcesContent": ["import lottie from \"lottie-web\";\nimport React, { useEffect, useRef, useState, } from \"react\";\nconst useLottie = (props, style) => {\n    const { animationData, loop, autoplay, initialSegment, onComplete, onLoopComplete, onEnterFrame, onSegmentStart, onConfigReady, onDataReady, onDataFailed, onLoadedImages, onDOMLoaded, onDestroy, \n    // Specified here to take them out from the 'rest'\n    lottieRef, renderer, name, assetsPath, rendererSettings, \n    // TODO: find a better way to extract the html props to avoid specifying\n    //  all the props that we want to exclude (as you can see above)\n    ...rest } = props;\n    const [animationLoaded, setAnimationLoaded] = useState(false);\n    const animationInstanceRef = useRef();\n    const animationContainer = useRef(null);\n    /*\n          ======================================\n              INTERACTION METHODS\n          ======================================\n       */\n    /**\n     * Play\n     */\n    const play = () => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.play();\n    };\n    /**\n     * Stop\n     */\n    const stop = () => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.stop();\n    };\n    /**\n     * Pause\n     */\n    const pause = () => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.pause();\n    };\n    /**\n     * Set animation speed\n     * @param speed\n     */\n    const setSpeed = (speed) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSpeed(speed);\n    };\n    /**\n     * Got to frame and play\n     * @param value\n     * @param isFrame\n     */\n    const goToAndPlay = (value, isFrame) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndPlay(value, isFrame);\n    };\n    /**\n     * Got to frame and stop\n     * @param value\n     * @param isFrame\n     */\n    const goToAndStop = (value, isFrame) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndStop(value, isFrame);\n    };\n    /**\n     * Set animation direction\n     * @param direction\n     */\n    const setDirection = (direction) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setDirection(direction);\n    };\n    /**\n     * Play animation segments\n     * @param segments\n     * @param forceFlag\n     */\n    const playSegments = (segments, forceFlag) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.playSegments(segments, forceFlag);\n    };\n    /**\n     * Set sub frames\n     * @param useSubFrames\n     */\n    const setSubframe = (useSubFrames) => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSubframe(useSubFrames);\n    };\n    /**\n     * Get animation duration\n     * @param inFrames\n     */\n    const getDuration = (inFrames) => { var _a; return (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.getDuration(inFrames); };\n    /**\n     * Destroy animation\n     */\n    const destroy = () => {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n        // Removing the reference to the animation so separate cleanups are skipped.\n        // Without it the internal `lottie-react` instance throws exceptions as it already cleared itself on destroy.\n        animationInstanceRef.current = undefined;\n    };\n    /*\n          ======================================\n              LOTTIE\n          ======================================\n       */\n    /**\n     * Load a new animation, and if it's the case, destroy the previous one\n     * @param {Object} forcedConfigs\n     */\n    const loadAnimation = (forcedConfigs = {}) => {\n        var _a;\n        // Return if the container ref is null\n        if (!animationContainer.current) {\n            return;\n        }\n        // Destroy any previous instance\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n        // Build the animation configuration\n        const config = {\n            ...props,\n            ...forcedConfigs,\n            container: animationContainer.current,\n        };\n        // Save the animation instance\n        animationInstanceRef.current = lottie.loadAnimation(config);\n        setAnimationLoaded(!!animationInstanceRef.current);\n        // Return a function that will clean up\n        return () => {\n            var _a;\n            (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n            animationInstanceRef.current = undefined;\n        };\n    };\n    /**\n     * (Re)Initialize when animation data changed\n     */\n    useEffect(() => {\n        const onUnmount = loadAnimation();\n        // Clean up on unmount\n        return () => onUnmount === null || onUnmount === void 0 ? void 0 : onUnmount();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [animationData, loop]);\n    // Update the autoplay state\n    useEffect(() => {\n        if (!animationInstanceRef.current) {\n            return;\n        }\n        animationInstanceRef.current.autoplay = !!autoplay;\n    }, [autoplay]);\n    // Update the initial segment state\n    useEffect(() => {\n        if (!animationInstanceRef.current) {\n            return;\n        }\n        // When null should reset to default animation length\n        if (!initialSegment) {\n            animationInstanceRef.current.resetSegments(true);\n            return;\n        }\n        // If it's not a valid segment, do nothing\n        if (!Array.isArray(initialSegment) || !initialSegment.length) {\n            return;\n        }\n        // If the current position it's not in the new segment\n        // set the current position to start\n        if (animationInstanceRef.current.currentRawFrame < initialSegment[0] ||\n            animationInstanceRef.current.currentRawFrame > initialSegment[1]) {\n            animationInstanceRef.current.currentRawFrame = initialSegment[0];\n        }\n        // Update the segment\n        animationInstanceRef.current.setSegment(initialSegment[0], initialSegment[1]);\n    }, [initialSegment]);\n    /*\n          ======================================\n              EVENTS\n          ======================================\n       */\n    /**\n     * Reinitialize listener on change\n     */\n    useEffect(() => {\n        const partialListeners = [\n            { name: \"complete\", handler: onComplete },\n            { name: \"loopComplete\", handler: onLoopComplete },\n            { name: \"enterFrame\", handler: onEnterFrame },\n            { name: \"segmentStart\", handler: onSegmentStart },\n            { name: \"config_ready\", handler: onConfigReady },\n            { name: \"data_ready\", handler: onDataReady },\n            { name: \"data_failed\", handler: onDataFailed },\n            { name: \"loaded_images\", handler: onLoadedImages },\n            { name: \"DOMLoaded\", handler: onDOMLoaded },\n            { name: \"destroy\", handler: onDestroy },\n        ];\n        const listeners = partialListeners.filter((listener) => listener.handler != null);\n        if (!listeners.length) {\n            return;\n        }\n        const deregisterList = listeners.map(\n        /**\n         * Handle the process of adding an event listener\n         * @param {Listener} listener\n         * @return {Function} Function that deregister the listener\n         */\n        (listener) => {\n            var _a;\n            (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.addEventListener(listener.name, listener.handler);\n            // Return a function to deregister this listener\n            return () => {\n                var _a;\n                (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(listener.name, listener.handler);\n            };\n        });\n        // Deregister listeners on unmount\n        return () => {\n            deregisterList.forEach((deregister) => deregister());\n        };\n    }, [\n        onComplete,\n        onLoopComplete,\n        onEnterFrame,\n        onSegmentStart,\n        onConfigReady,\n        onDataReady,\n        onDataFailed,\n        onLoadedImages,\n        onDOMLoaded,\n        onDestroy,\n    ]);\n    /**\n     * Build the animation view\n     */\n    const View = React.createElement(\"div\", { style: style, ref: animationContainer, ...rest });\n    return {\n        View,\n        play,\n        stop,\n        pause,\n        setSpeed,\n        goToAndStop,\n        goToAndPlay,\n        setDirection,\n        playSegments,\n        setSubframe,\n        getDuration,\n        destroy,\n        animationContainerRef: animationContainer,\n        animationLoaded,\n        animationItem: animationInstanceRef.current,\n    };\n};\nexport default useLottie;\n", "import { useEffect } from \"react\";\n// helpers\nexport function getContainerVisibility(container) {\n    const { top, height } = container.getBoundingClientRect();\n    const current = window.innerHeight - top;\n    const max = window.innerHeight + height;\n    return current / max;\n}\nexport function getContainerCursorPosition(container, cursorX, cursorY) {\n    const { top, left, width, height } = container.getBoundingClientRect();\n    const x = (cursorX - left) / width;\n    const y = (cursorY - top) / height;\n    return { x, y };\n}\nexport const useInitInteractivity = ({ wrapperRef, animationItem, mode, actions, }) => {\n    useEffect(() => {\n        const wrapper = wrapperRef.current;\n        if (!wrapper || !animationItem || !actions.length) {\n            return;\n        }\n        animationItem.stop();\n        const scrollModeHandler = () => {\n            let assignedSegment = null;\n            const scrollHandler = () => {\n                const currentPercent = getContainerVisibility(wrapper);\n                // Find the first action that satisfies the current position conditions\n                const action = actions.find(({ visibility }) => visibility &&\n                    currentPercent >= visibility[0] &&\n                    currentPercent <= visibility[1]);\n                // Skip if no matching action was found!\n                if (!action) {\n                    return;\n                }\n                if (action.type === \"seek\" &&\n                    action.visibility &&\n                    action.frames.length === 2) {\n                    // Seek: Go to a frame based on player scroll position action\n                    const frameToGo = action.frames[0] +\n                        Math.ceil(((currentPercent - action.visibility[0]) /\n                            (action.visibility[1] - action.visibility[0])) *\n                            action.frames[1]);\n                    //! goToAndStop must be relative to the start of the current segment\n                    animationItem.goToAndStop(frameToGo - animationItem.firstFrame - 1, true);\n                }\n                if (action.type === \"loop\") {\n                    // Loop: Loop a given frames\n                    if (assignedSegment === null) {\n                        // if not playing any segments currently. play those segments and save to state\n                        animationItem.playSegments(action.frames, true);\n                        assignedSegment = action.frames;\n                    }\n                    else {\n                        // if playing any segments currently.\n                        //check if segments in state are equal to the frames selected by action\n                        if (assignedSegment !== action.frames) {\n                            // if they are not equal. new segments are to be loaded\n                            animationItem.playSegments(action.frames, true);\n                            assignedSegment = action.frames;\n                        }\n                        else if (animationItem.isPaused) {\n                            // if they are equal the play method must be called only if lottie is paused\n                            animationItem.playSegments(action.frames, true);\n                            assignedSegment = action.frames;\n                        }\n                    }\n                }\n                if (action.type === \"play\" && animationItem.isPaused) {\n                    // Play: Reset segments and continue playing full animation from current position\n                    animationItem.resetSegments(true);\n                    animationItem.play();\n                }\n                if (action.type === \"stop\") {\n                    // Stop: Stop playback\n                    animationItem.goToAndStop(action.frames[0] - animationItem.firstFrame - 1, true);\n                }\n            };\n            document.addEventListener(\"scroll\", scrollHandler);\n            return () => {\n                document.removeEventListener(\"scroll\", scrollHandler);\n            };\n        };\n        const cursorModeHandler = () => {\n            const handleCursor = (_x, _y) => {\n                let x = _x;\n                let y = _y;\n                // Resolve cursor position if cursor is inside container\n                if (x !== -1 && y !== -1) {\n                    // Get container cursor position\n                    const pos = getContainerCursorPosition(wrapper, x, y);\n                    // Use the resolved position\n                    x = pos.x;\n                    y = pos.y;\n                }\n                // Find the first action that satisfies the current position conditions\n                const action = actions.find(({ position }) => {\n                    if (position &&\n                        Array.isArray(position.x) &&\n                        Array.isArray(position.y)) {\n                        return (x >= position.x[0] &&\n                            x <= position.x[1] &&\n                            y >= position.y[0] &&\n                            y <= position.y[1]);\n                    }\n                    if (position &&\n                        !Number.isNaN(position.x) &&\n                        !Number.isNaN(position.y)) {\n                        return x === position.x && y === position.y;\n                    }\n                    return false;\n                });\n                // Skip if no matching action was found!\n                if (!action) {\n                    return;\n                }\n                // Process action types:\n                if (action.type === \"seek\" &&\n                    action.position &&\n                    Array.isArray(action.position.x) &&\n                    Array.isArray(action.position.y) &&\n                    action.frames.length === 2) {\n                    // Seek: Go to a frame based on player scroll position action\n                    const xPercent = (x - action.position.x[0]) /\n                        (action.position.x[1] - action.position.x[0]);\n                    const yPercent = (y - action.position.y[0]) /\n                        (action.position.y[1] - action.position.y[0]);\n                    animationItem.playSegments(action.frames, true);\n                    animationItem.goToAndStop(Math.ceil(((xPercent + yPercent) / 2) *\n                        (action.frames[1] - action.frames[0])), true);\n                }\n                if (action.type === \"loop\") {\n                    animationItem.playSegments(action.frames, true);\n                }\n                if (action.type === \"play\") {\n                    // Play: Reset segments and continue playing full animation from current position\n                    if (animationItem.isPaused) {\n                        animationItem.resetSegments(false);\n                    }\n                    animationItem.playSegments(action.frames);\n                }\n                if (action.type === \"stop\") {\n                    animationItem.goToAndStop(action.frames[0], true);\n                }\n            };\n            const mouseMoveHandler = (ev) => {\n                handleCursor(ev.clientX, ev.clientY);\n            };\n            const mouseOutHandler = () => {\n                handleCursor(-1, -1);\n            };\n            wrapper.addEventListener(\"mousemove\", mouseMoveHandler);\n            wrapper.addEventListener(\"mouseout\", mouseOutHandler);\n            return () => {\n                wrapper.removeEventListener(\"mousemove\", mouseMoveHandler);\n                wrapper.removeEventListener(\"mouseout\", mouseOutHandler);\n            };\n        };\n        switch (mode) {\n            case \"scroll\":\n                return scrollModeHandler();\n            case \"cursor\":\n                return cursorModeHandler();\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [mode, animationItem]);\n};\nconst useLottieInteractivity = ({ actions, mode, lottieObj, }) => {\n    const { animationItem, View, animationContainerRef } = lottieObj;\n    useInitInteractivity({\n        actions,\n        animationItem,\n        mode,\n        wrapperRef: animationContainerRef,\n    });\n    return View;\n};\nexport default useLottieInteractivity;\n", "import { useEffect } from \"react\";\nimport useLottie from \"../hooks/useLottie\";\nimport useLottieInteractivity from \"../hooks/useLottieInteractivity\";\nconst Lottie = (props) => {\n    var _a, _b, _c;\n    const { style, interactivity, ...lottieProps } = props;\n    /**\n     * Initialize the 'useLottie' hook\n     */\n    const { View, play, stop, pause, setSpeed, goToAndStop, goToAndPlay, setDirection, playSegments, setSubframe, getDuration, destroy, animationContainerRef, animationLoaded, animationItem, } = useLottie(lottieProps, style);\n    /**\n     * Make the hook variables/methods available through the provided 'lottieRef'\n     */\n    useEffect(() => {\n        if (props.lottieRef) {\n            props.lottieRef.current = {\n                play,\n                stop,\n                pause,\n                setSpeed,\n                goToAndPlay,\n                goToAndStop,\n                setDirection,\n                playSegments,\n                setSubframe,\n                getDuration,\n                destroy,\n                animationContainerRef,\n                animationLoaded,\n                animationItem,\n            };\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [(_a = props.lottieRef) === null || _a === void 0 ? void 0 : _a.current]);\n    return useLottieInteractivity({\n        lottieObj: {\n            View,\n            play,\n            stop,\n            pause,\n            setSpeed,\n            goToAndStop,\n            goToAndPlay,\n            setDirection,\n            playSegments,\n            setSubframe,\n            getDuration,\n            destroy,\n            animationContainerRef,\n            animationLoaded,\n            animationItem,\n        },\n        actions: (_b = interactivity === null || interactivity === void 0 ? void 0 : interactivity.actions) !== null && _b !== void 0 ? _b : [],\n        mode: (_c = interactivity === null || interactivity === void 0 ? void 0 : interactivity.mode) !== null && _c !== void 0 ? _c : \"scroll\",\n    });\n};\nexport default Lottie;\n"], "names": ["useLottie", "props", "style", "animationData", "loop", "autoplay", "initialSegment", "onComplete", "onLoopComplete", "onEnterFrame", "onSegmentStart", "onConfigReady", "onDataReady", "onDataFailed", "onLoadedImages", "onDOMLoaded", "onDestroy", "lottieRef", "renderer", "name", "assetsPath", "rendererSettings", "rest", "_objectWithoutProperties", "_excluded", "_useState", "useState", "_useState2", "_slicedToArray", "animationLoaded", "setAnimationLoaded", "animationInstanceRef", "useRef", "animationContainer", "play", "_a", "current", "stop", "pause", "setSpeed", "speed", "goToAndPlay", "value", "isFrame", "goToAndStop", "setDirection", "direction", "playSegments", "segments", "forceFlag", "setSubframe", "useSubFrames", "getDuration", "inFrames", "destroy", "undefined", "loadAnimation", "forcedConfigs", "arguments", "length", "config", "_objectSpread", "container", "lottie", "useEffect", "onUnmount", "resetSegments", "Array", "isArray", "currentRawFrame", "setSegment", "partialListeners", "handler", "listeners", "filter", "listener", "deregisterList", "map", "addEventListener", "removeEventListener", "for<PERSON>ach", "deregister", "View", "React", "createElement", "ref", "animationContainerRef", "animationItem", "getContainerVisibility", "_container$getBoundin", "getBoundingClientRect", "top", "height", "window", "innerHeight", "max", "getContainerCursorPosition", "cursorX", "cursorY", "_container$getBoundin2", "left", "width", "x", "y", "useInitInteractivity", "_ref", "wrapperRef", "mode", "actions", "wrapper", "scrollModeHandler", "assignedSegment", "<PERSON><PERSON><PERSON><PERSON>", "currentPercent", "action", "find", "_ref2", "visibility", "type", "frames", "frameToGo", "Math", "ceil", "firstFrame", "isPaused", "document", "cursor<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleCursor", "_x", "_y", "pos", "_ref3", "position", "Number", "isNaN", "xPercent", "yPercent", "mouseMoveHandler", "ev", "clientX", "clientY", "mouseOutHandler", "useLottieInteractivity", "_ref4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "_b", "_c", "interactivity", "lottieProps", "_useL<PERSON>ie"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEMA,MAAAA,SAAS,GAAG,SAAZA,SAASA,CAAIC,KAAK,EAAEC,KAAK,EAAK;EAChC,EAAA,IAAQC,aAAa,GAKTF,KAAK,CALTE,aAAa,CAAA;MAAEC,IAAI,GAKfH,KAAK,CALMG,IAAI,CAAA;MAAEC,QAAQ,GAKzBJ,KAAK,CALYI,QAAQ,CAAA;MAAEC,cAAc,GAKzCL,KAAK,CALsBK,cAAc,CAAA;MAAEC,UAAU,GAKrDN,KAAK,CALsCM,UAAU,CAAA;MAAEC,cAAc,GAKrEP,KAAK,CALkDO,cAAc,CAAA;MAAEC,YAAY,GAKnFR,KAAK,CALkEQ,YAAY,CAAA;MAAEC,cAAc,GAKnGT,KAAK,CALgFS,cAAc,CAAA;MAAEC,aAAa,GAKlHV,KAAK,CALgGU,aAAa,CAAA;MAAEC,WAAW,GAK/HX,KAAK,CAL+GW,WAAW,CAAA;MAAEC,YAAY,GAK7IZ,KAAK,CAL4HY,YAAY,CAAA;MAAEC,cAAc,GAK7Jb,KAAK,CAL0Ia,cAAc,CAAA;MAAEC,WAAW,GAK1Kd,KAAK,CAL0Jc,WAAW,CAAA;MAAEC,SAAS,GAKrLf,KAAK,CALuKe,SAAS,CAAA;MAKrLf,KAAK,CAHjBgB,SAAS,CAAA;MAGGhB,KAAK,CAHNiB,QAAQ,CAAA;MAGPjB,KAAK,CAHIkB,IAAI,CAAA;MAGblB,KAAK,CAHUmB,UAAU,CAAA;MAGzBnB,KAAK,CAHsBoB,gBAAgB,CAAA;EAGpDC,QAAAA,IAAI,GAAAC,wBAAA,CAAKtB,KAAK,EAAAuB,WAAA,EAAA;EACjB,EAAA,IAAAC,SAAA,GAA8CC,cAAQ,CAAC,KAAK,CAAC;MAAAC,UAAA,GAAAC,cAAA,CAAAH,SAAA,EAAA,CAAA,CAAA;EAAtDI,IAAAA,eAAe,GAAAF,UAAA,CAAA,CAAA,CAAA;EAAEG,IAAAA,kBAAkB,GAAAH,UAAA,CAAA,CAAA,CAAA,CAAA;EAC1C,EAAA,IAAMI,oBAAoB,GAAGC,YAAM,EAAE,CAAA;EACrC,EAAA,IAAMC,kBAAkB,GAAGD,YAAM,CAAC,IAAI,CAAC,CAAA;EACvC;EACJ;EACA;EACA;EACA;EACI;EACJ;EACA;EACI,EAAA,IAAME,IAAI,GAAG,SAAPA,IAAIA,GAAS;EACf,IAAA,IAAIC,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,IAAI,EAAE,CAAA;KACrF,CAAA;EACD;EACJ;EACA;EACI,EAAA,IAAMG,IAAI,GAAG,SAAPA,IAAIA,GAAS;EACf,IAAA,IAAIF,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,EAAE,CAAA;KACrF,CAAA;EACD;EACJ;EACA;EACI,EAAA,IAAMC,KAAK,GAAG,SAARA,KAAKA,GAAS;EAChB,IAAA,IAAIH,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,KAAK,EAAE,CAAA;KACtF,CAAA;EACD;EACJ;EACA;EACA;EACI,EAAA,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,KAAK,EAAK;EACxB,IAAA,IAAIL,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,CAACC,KAAK,CAAC,CAAA;KAC9F,CAAA;EACD;EACJ;EACA;EACA;EACA;IACI,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,KAAK,EAAEC,OAAO,EAAK;EACpC,IAAA,IAAIR,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC,CAAA;KAC1G,CAAA;EACD;EACJ;EACA;EACA;EACA;IACI,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIF,KAAK,EAAEC,OAAO,EAAK;EACpC,IAAA,IAAIR,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,WAAW,CAACF,KAAK,EAAEC,OAAO,CAAC,CAAA;KAC1G,CAAA;EACD;EACJ;EACA;EACA;EACI,EAAA,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAIC,SAAS,EAAK;EAChC,IAAA,IAAIX,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,YAAY,CAACC,SAAS,CAAC,CAAA;KACtG,CAAA;EACD;EACJ;EACA;EACA;EACA;IACI,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,QAAQ,EAAEC,SAAS,EAAK;EAC1C,IAAA,IAAId,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,YAAY,CAACC,QAAQ,EAAEC,SAAS,CAAC,CAAA;KAChH,CAAA;EACD;EACJ;EACA;EACA;EACI,EAAA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,YAAY,EAAK;EAClC,IAAA,IAAIhB,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,WAAW,CAACC,YAAY,CAAC,CAAA;KACxG,CAAA;EACD;EACJ;EACA;EACA;EACI,EAAA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,QAAQ,EAAK;EAAE,IAAA,IAAIlB,EAAE,CAAA;MAAE,OAAO,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,WAAW,CAACC,QAAQ,CAAC,CAAA;KAAG,CAAA;EACvJ;EACJ;EACA;EACI,EAAA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,GAAS;EAClB,IAAA,IAAInB,EAAE,CAAA;MACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,OAAO,EAAE,CAAA;EACrF;EACA;MACAvB,oBAAoB,CAACK,OAAO,GAAGmB,SAAS,CAAA;KAC3C,CAAA;EACD;EACJ;EACA;EACA;EACA;EACI;EACJ;EACA;EACA;EACI,EAAA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,GAA2B;EAAA,IAAA,IAAvBC,aAAa,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAH,SAAA,GAAAG,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;EACrC,IAAA,IAAIvB,EAAE,CAAA;EACN;EACA,IAAA,IAAI,CAACF,kBAAkB,CAACG,OAAO,EAAE;EAC7B,MAAA,OAAA;EACJ,KAAA;EACA;MACA,CAACD,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,OAAO,EAAE,CAAA;EACrF;MACA,IAAMM,MAAM,GAAAC,cAAA,CAAAA,cAAA,CAAAA,cAAA,CAAA,EAAA,EACL5D,KAAK,CAAA,EACLwD,aAAa,CAAA,EAAA,EAAA,EAAA;QAChBK,SAAS,EAAE7B,kBAAkB,CAACG,OAAAA;OACjC,CAAA,CAAA;EACD;MACAL,oBAAoB,CAACK,OAAO,GAAG2B,0BAAM,CAACP,aAAa,CAACI,MAAM,CAAC,CAAA;EAC3D9B,IAAAA,kBAAkB,CAAC,CAAC,CAACC,oBAAoB,CAACK,OAAO,CAAC,CAAA;EAClD;EACA,IAAA,OAAO,YAAM;EACT,MAAA,IAAID,EAAE,CAAA;QACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,OAAO,EAAE,CAAA;QACrFvB,oBAAoB,CAACK,OAAO,GAAGmB,SAAS,CAAA;OAC3C,CAAA;KACJ,CAAA;EACD;EACJ;EACA;EACIS,EAAAA,eAAS,CAAC,YAAM;EACZ,IAAA,IAAMC,SAAS,GAAGT,aAAa,EAAE,CAAA;EACjC;MACA,OAAO,YAAA;EAAA,MAAA,OAAMS,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,EAAE,CAAA;EAAA,KAAA,CAAA;EAC9E;EACJ,GAAC,EAAE,CAAC9D,aAAa,EAAEC,IAAI,CAAC,CAAC,CAAA;EACzB;EACA4D,EAAAA,eAAS,CAAC,YAAM;EACZ,IAAA,IAAI,CAACjC,oBAAoB,CAACK,OAAO,EAAE;EAC/B,MAAA,OAAA;EACJ,KAAA;EACAL,IAAAA,oBAAoB,CAACK,OAAO,CAAC/B,QAAQ,GAAG,CAAC,CAACA,QAAQ,CAAA;EACtD,GAAC,EAAE,CAACA,QAAQ,CAAC,CAAC,CAAA;EACd;EACA2D,EAAAA,eAAS,CAAC,YAAM;EACZ,IAAA,IAAI,CAACjC,oBAAoB,CAACK,OAAO,EAAE;EAC/B,MAAA,OAAA;EACJ,KAAA;EACA;MACA,IAAI,CAAC9B,cAAc,EAAE;EACjByB,MAAAA,oBAAoB,CAACK,OAAO,CAAC8B,aAAa,CAAC,IAAI,CAAC,CAAA;EAChD,MAAA,OAAA;EACJ,KAAA;EACA;EACA,IAAA,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC9D,cAAc,CAAC,IAAI,CAACA,cAAc,CAACqD,MAAM,EAAE;EAC1D,MAAA,OAAA;EACJ,KAAA;EACA;EACA;MACA,IAAI5B,oBAAoB,CAACK,OAAO,CAACiC,eAAe,GAAG/D,cAAc,CAAC,CAAC,CAAC,IAChEyB,oBAAoB,CAACK,OAAO,CAACiC,eAAe,GAAG/D,cAAc,CAAC,CAAC,CAAC,EAAE;QAClEyB,oBAAoB,CAACK,OAAO,CAACiC,eAAe,GAAG/D,cAAc,CAAC,CAAC,CAAC,CAAA;EACpE,KAAA;EACA;EACAyB,IAAAA,oBAAoB,CAACK,OAAO,CAACkC,UAAU,CAAChE,cAAc,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA;EACjF,GAAC,EAAE,CAACA,cAAc,CAAC,CAAC,CAAA;EACpB;EACJ;EACA;EACA;EACA;EACI;EACJ;EACA;EACI0D,EAAAA,eAAS,CAAC,YAAM;MACZ,IAAMO,gBAAgB,GAAG,CACrB;EAAEpD,MAAAA,IAAI,EAAE,UAAU;EAAEqD,MAAAA,OAAO,EAAEjE,UAAAA;EAAW,KAAC,EACzC;EAAEY,MAAAA,IAAI,EAAE,cAAc;EAAEqD,MAAAA,OAAO,EAAEhE,cAAAA;EAAe,KAAC,EACjD;EAAEW,MAAAA,IAAI,EAAE,YAAY;EAAEqD,MAAAA,OAAO,EAAE/D,YAAAA;EAAa,KAAC,EAC7C;EAAEU,MAAAA,IAAI,EAAE,cAAc;EAAEqD,MAAAA,OAAO,EAAE9D,cAAAA;EAAe,KAAC,EACjD;EAAES,MAAAA,IAAI,EAAE,cAAc;EAAEqD,MAAAA,OAAO,EAAE7D,aAAAA;EAAc,KAAC,EAChD;EAAEQ,MAAAA,IAAI,EAAE,YAAY;EAAEqD,MAAAA,OAAO,EAAE5D,WAAAA;EAAY,KAAC,EAC5C;EAAEO,MAAAA,IAAI,EAAE,aAAa;EAAEqD,MAAAA,OAAO,EAAE3D,YAAAA;EAAa,KAAC,EAC9C;EAAEM,MAAAA,IAAI,EAAE,eAAe;EAAEqD,MAAAA,OAAO,EAAE1D,cAAAA;EAAe,KAAC,EAClD;EAAEK,MAAAA,IAAI,EAAE,WAAW;EAAEqD,MAAAA,OAAO,EAAEzD,WAAAA;EAAY,KAAC,EAC3C;EAAEI,MAAAA,IAAI,EAAE,SAAS;EAAEqD,MAAAA,OAAO,EAAExD,SAAAA;EAAU,KAAC,CAC1C,CAAA;EACD,IAAA,IAAMyD,SAAS,GAAGF,gBAAgB,CAACG,MAAM,CAAC,UAACC,QAAQ,EAAA;EAAA,MAAA,OAAKA,QAAQ,CAACH,OAAO,IAAI,IAAI,CAAA;OAAC,CAAA,CAAA;EACjF,IAAA,IAAI,CAACC,SAAS,CAACd,MAAM,EAAE;EACnB,MAAA,OAAA;EACJ,KAAA;EACA,IAAA,IAAMiB,cAAc,GAAGH,SAAS,CAACI,GAAG;EACpC;EACR;EACA;EACA;EACA;EACQ,IAAA,UAACF,QAAQ,EAAK;EACV,MAAA,IAAIxC,EAAE,CAAA;QACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2C,gBAAgB,CAACH,QAAQ,CAACxD,IAAI,EAAEwD,QAAQ,CAACH,OAAO,CAAC,CAAA;EAC7H;EACA,MAAA,OAAO,YAAM;EACT,QAAA,IAAIrC,EAAE,CAAA;UACN,CAACA,EAAE,GAAGJ,oBAAoB,CAACK,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4C,mBAAmB,CAACJ,QAAQ,CAACxD,IAAI,EAAEwD,QAAQ,CAACH,OAAO,CAAC,CAAA;SACnI,CAAA;EACL,KAAC,CAAC,CAAA;EACF;EACA,IAAA,OAAO,YAAM;EACTI,MAAAA,cAAc,CAACI,OAAO,CAAC,UAACC,UAAU,EAAA;UAAA,OAAKA,UAAU,EAAE,CAAA;SAAC,CAAA,CAAA;OACvD,CAAA;KACJ,EAAE,CACC1E,UAAU,EACVC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,SAAS,CACZ,CAAC,CAAA;EACF;EACJ;EACA;IACI,IAAMkE,IAAI,gBAAGC,yBAAK,CAACC,aAAa,CAAC,KAAK,EAAAvB,cAAA,CAAA;EAAI3D,IAAAA,KAAK,EAAEA,KAAK;EAAEmF,IAAAA,GAAG,EAAEpD,kBAAAA;KAAuBX,EAAAA,IAAI,CAAE,CAAC,CAAA;IAC3F,OAAO;EACH4D,IAAAA,IAAI,EAAJA,IAAI;EACJhD,IAAAA,IAAI,EAAJA,IAAI;EACJG,IAAAA,IAAI,EAAJA,IAAI;EACJC,IAAAA,KAAK,EAALA,KAAK;EACLC,IAAAA,QAAQ,EAARA,QAAQ;EACRK,IAAAA,WAAW,EAAXA,WAAW;EACXH,IAAAA,WAAW,EAAXA,WAAW;EACXI,IAAAA,YAAY,EAAZA,YAAY;EACZE,IAAAA,YAAY,EAAZA,YAAY;EACZG,IAAAA,WAAW,EAAXA,WAAW;EACXE,IAAAA,WAAW,EAAXA,WAAW;EACXE,IAAAA,OAAO,EAAPA,OAAO;EACPgC,IAAAA,qBAAqB,EAAErD,kBAAkB;EACzCJ,IAAAA,eAAe,EAAfA,eAAe;MACf0D,aAAa,EAAExD,oBAAoB,CAACK,OAAAA;KACvC,CAAA;EACL;;EC5PA;EACO,SAASoD,sBAAsBA,CAAC1B,SAAS,EAAE;EAC9C,EAAA,IAAA2B,qBAAA,GAAwB3B,SAAS,CAAC4B,qBAAqB,EAAE;MAAjDC,GAAG,GAAAF,qBAAA,CAAHE,GAAG;MAAEC,MAAM,GAAAH,qBAAA,CAANG,MAAM,CAAA;EACnB,EAAA,IAAMxD,OAAO,GAAGyD,MAAM,CAACC,WAAW,GAAGH,GAAG,CAAA;EACxC,EAAA,IAAMI,GAAG,GAAGF,MAAM,CAACC,WAAW,GAAGF,MAAM,CAAA;IACvC,OAAOxD,OAAO,GAAG2D,GAAG,CAAA;EACxB,CAAA;EACO,SAASC,0BAA0BA,CAAClC,SAAS,EAAEmC,OAAO,EAAEC,OAAO,EAAE;EACpE,EAAA,IAAAC,sBAAA,GAAqCrC,SAAS,CAAC4B,qBAAqB,EAAE;MAA9DC,GAAG,GAAAQ,sBAAA,CAAHR,GAAG;MAAES,IAAI,GAAAD,sBAAA,CAAJC,IAAI;MAAEC,KAAK,GAAAF,sBAAA,CAALE,KAAK;MAAET,MAAM,GAAAO,sBAAA,CAANP,MAAM,CAAA;EAChC,EAAA,IAAMU,CAAC,GAAG,CAACL,OAAO,GAAGG,IAAI,IAAIC,KAAK,CAAA;EAClC,EAAA,IAAME,CAAC,GAAG,CAACL,OAAO,GAAGP,GAAG,IAAIC,MAAM,CAAA;IAClC,OAAO;EAAEU,IAAAA,CAAC,EAADA,CAAC;EAAEC,IAAAA,CAAC,EAADA,CAAAA;KAAG,CAAA;EACnB,CAAA;EACO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAAC,IAAA,EAAsD;EAAA,EAAA,IAAhDC,UAAU,GAAAD,IAAA,CAAVC,UAAU;MAAEnB,aAAa,GAAAkB,IAAA,CAAblB,aAAa;MAAEoB,IAAI,GAAAF,IAAA,CAAJE,IAAI;MAAEC,OAAO,GAAAH,IAAA,CAAPG,OAAO,CAAA;EAC3E5C,EAAAA,eAAS,CAAC,YAAM;EACZ,IAAA,IAAM6C,OAAO,GAAGH,UAAU,CAACtE,OAAO,CAAA;MAClC,IAAI,CAACyE,OAAO,IAAI,CAACtB,aAAa,IAAI,CAACqB,OAAO,CAACjD,MAAM,EAAE;EAC/C,MAAA,OAAA;EACJ,KAAA;MACA4B,aAAa,CAAClD,IAAI,EAAE,CAAA;EACpB,IAAA,IAAMyE,iBAAiB,GAAG,SAApBA,iBAAiBA,GAAS;QAC5B,IAAIC,eAAe,GAAG,IAAI,CAAA;EAC1B,MAAA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,GAAS;EACxB,QAAA,IAAMC,cAAc,GAAGzB,sBAAsB,CAACqB,OAAO,CAAC,CAAA;EACtD;EACA,QAAA,IAAMK,MAAM,GAAGN,OAAO,CAACO,IAAI,CAAC,UAAAC,KAAA,EAAA;EAAA,UAAA,IAAGC,UAAU,GAAAD,KAAA,CAAVC,UAAU,CAAA;EAAA,UAAA,OAAOA,UAAU,IACtDJ,cAAc,IAAII,UAAU,CAAC,CAAC,CAAC,IAC/BJ,cAAc,IAAII,UAAU,CAAC,CAAC,CAAC,CAAA;WAAC,CAAA,CAAA;EACpC;UACA,IAAI,CAACH,MAAM,EAAE;EACT,UAAA,OAAA;EACJ,SAAA;EACA,QAAA,IAAIA,MAAM,CAACI,IAAI,KAAK,MAAM,IACtBJ,MAAM,CAACG,UAAU,IACjBH,MAAM,CAACK,MAAM,CAAC5D,MAAM,KAAK,CAAC,EAAE;EAC5B;YACA,IAAM6D,SAAS,GAAGN,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,GAC9BE,IAAI,CAACC,IAAI,CAAE,CAACT,cAAc,GAAGC,MAAM,CAACG,UAAU,CAAC,CAAC,CAAC,KAC5CH,MAAM,CAACG,UAAU,CAAC,CAAC,CAAC,GAAGH,MAAM,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,GAC7CH,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;EACzB;EACAhC,UAAAA,aAAa,CAAC3C,WAAW,CAAC4E,SAAS,GAAGjC,aAAa,CAACoC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;EAC7E,SAAA;EACA,QAAA,IAAIT,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;EACxB;YACA,IAAIP,eAAe,KAAK,IAAI,EAAE;EAC1B;cACAxB,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;cAC/CR,eAAe,GAAGG,MAAM,CAACK,MAAM,CAAA;EACnC,WAAC,MACI;EACD;EACA;EACA,YAAA,IAAIR,eAAe,KAAKG,MAAM,CAACK,MAAM,EAAE;EACnC;gBACAhC,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;gBAC/CR,eAAe,GAAGG,MAAM,CAACK,MAAM,CAAA;EACnC,aAAC,MACI,IAAIhC,aAAa,CAACqC,QAAQ,EAAE;EAC7B;gBACArC,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;gBAC/CR,eAAe,GAAGG,MAAM,CAACK,MAAM,CAAA;EACnC,aAAA;EACJ,WAAA;EACJ,SAAA;UACA,IAAIL,MAAM,CAACI,IAAI,KAAK,MAAM,IAAI/B,aAAa,CAACqC,QAAQ,EAAE;EAClD;EACArC,UAAAA,aAAa,CAACrB,aAAa,CAAC,IAAI,CAAC,CAAA;YACjCqB,aAAa,CAACrD,IAAI,EAAE,CAAA;EACxB,SAAA;EACA,QAAA,IAAIgF,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;EACxB;EACA/B,UAAAA,aAAa,CAAC3C,WAAW,CAACsE,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,GAAGhC,aAAa,CAACoC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;EACpF,SAAA;SACH,CAAA;EACDE,MAAAA,QAAQ,CAAC/C,gBAAgB,CAAC,QAAQ,EAAEkC,aAAa,CAAC,CAAA;EAClD,MAAA,OAAO,YAAM;EACTa,QAAAA,QAAQ,CAAC9C,mBAAmB,CAAC,QAAQ,EAAEiC,aAAa,CAAC,CAAA;SACxD,CAAA;OACJ,CAAA;EACD,IAAA,IAAMc,iBAAiB,GAAG,SAApBA,iBAAiBA,GAAS;QAC5B,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,EAAE,EAAEC,EAAE,EAAK;UAC7B,IAAI3B,CAAC,GAAG0B,EAAE,CAAA;UACV,IAAIzB,CAAC,GAAG0B,EAAE,CAAA;EACV;UACA,IAAI3B,CAAC,KAAK,CAAC,CAAC,IAAIC,CAAC,KAAK,CAAC,CAAC,EAAE;EACtB;YACA,IAAM2B,GAAG,GAAGlC,0BAA0B,CAACa,OAAO,EAAEP,CAAC,EAAEC,CAAC,CAAC,CAAA;EACrD;YACAD,CAAC,GAAG4B,GAAG,CAAC5B,CAAC,CAAA;YACTC,CAAC,GAAG2B,GAAG,CAAC3B,CAAC,CAAA;EACb,SAAA;EACA;UACA,IAAMW,MAAM,GAAGN,OAAO,CAACO,IAAI,CAAC,UAAAgB,KAAA,EAAkB;EAAA,UAAA,IAAfC,QAAQ,GAAAD,KAAA,CAARC,QAAQ,CAAA;EACnC,UAAA,IAAIA,QAAQ,IACRjE,KAAK,CAACC,OAAO,CAACgE,QAAQ,CAAC9B,CAAC,CAAC,IACzBnC,KAAK,CAACC,OAAO,CAACgE,QAAQ,CAAC7B,CAAC,CAAC,EAAE;EAC3B,YAAA,OAAQD,CAAC,IAAI8B,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,IACtBA,CAAC,IAAI8B,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,IAClBC,CAAC,IAAI6B,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,IAClBA,CAAC,IAAI6B,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,CAAA;EAC1B,WAAA;YACA,IAAI6B,QAAQ,IACR,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC9B,CAAC,CAAC,IACzB,CAAC+B,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC7B,CAAC,CAAC,EAAE;cAC3B,OAAOD,CAAC,KAAK8B,QAAQ,CAAC9B,CAAC,IAAIC,CAAC,KAAK6B,QAAQ,CAAC7B,CAAC,CAAA;EAC/C,WAAA;EACA,UAAA,OAAO,KAAK,CAAA;EAChB,SAAC,CAAC,CAAA;EACF;UACA,IAAI,CAACW,MAAM,EAAE;EACT,UAAA,OAAA;EACJ,SAAA;EACA;EACA,QAAA,IAAIA,MAAM,CAACI,IAAI,KAAK,MAAM,IACtBJ,MAAM,CAACkB,QAAQ,IACfjE,KAAK,CAACC,OAAO,CAAC8C,MAAM,CAACkB,QAAQ,CAAC9B,CAAC,CAAC,IAChCnC,KAAK,CAACC,OAAO,CAAC8C,MAAM,CAACkB,QAAQ,CAAC7B,CAAC,CAAC,IAChCW,MAAM,CAACK,MAAM,CAAC5D,MAAM,KAAK,CAAC,EAAE;EAC5B;EACA,UAAA,IAAM4E,QAAQ,GAAG,CAACjC,CAAC,GAAGY,MAAM,CAACkB,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,KACrCY,MAAM,CAACkB,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,GAAGY,MAAM,CAACkB,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;EACjD,UAAA,IAAMkC,QAAQ,GAAG,CAACjC,CAAC,GAAGW,MAAM,CAACkB,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,KACrCW,MAAM,CAACkB,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,GAAGW,MAAM,CAACkB,QAAQ,CAAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACjDhB,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;EAC/ChC,UAAAA,aAAa,CAAC3C,WAAW,CAAC6E,IAAI,CAACC,IAAI,CAAE,CAACa,QAAQ,GAAGC,QAAQ,IAAI,CAAC,IACzDtB,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,GAAGL,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;EACrD,SAAA;EACA,QAAA,IAAIL,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;YACxB/B,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,EAAE,IAAI,CAAC,CAAA;EACnD,SAAA;EACA,QAAA,IAAIL,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;EACxB;YACA,IAAI/B,aAAa,CAACqC,QAAQ,EAAE;EACxBrC,YAAAA,aAAa,CAACrB,aAAa,CAAC,KAAK,CAAC,CAAA;EACtC,WAAA;EACAqB,UAAAA,aAAa,CAACxC,YAAY,CAACmE,MAAM,CAACK,MAAM,CAAC,CAAA;EAC7C,SAAA;EACA,QAAA,IAAIL,MAAM,CAACI,IAAI,KAAK,MAAM,EAAE;YACxB/B,aAAa,CAAC3C,WAAW,CAACsE,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;EACrD,SAAA;SACH,CAAA;EACD,MAAA,IAAMkB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,EAAE,EAAK;UAC7BX,YAAY,CAACW,EAAE,CAACC,OAAO,EAAED,EAAE,CAACE,OAAO,CAAC,CAAA;SACvC,CAAA;EACD,MAAA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,GAAS;EAC1Bd,QAAAA,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;SACvB,CAAA;EACDlB,MAAAA,OAAO,CAAC/B,gBAAgB,CAAC,WAAW,EAAE2D,gBAAgB,CAAC,CAAA;EACvD5B,MAAAA,OAAO,CAAC/B,gBAAgB,CAAC,UAAU,EAAE+D,eAAe,CAAC,CAAA;EACrD,MAAA,OAAO,YAAM;EACThC,QAAAA,OAAO,CAAC9B,mBAAmB,CAAC,WAAW,EAAE0D,gBAAgB,CAAC,CAAA;EAC1D5B,QAAAA,OAAO,CAAC9B,mBAAmB,CAAC,UAAU,EAAE8D,eAAe,CAAC,CAAA;SAC3D,CAAA;OACJ,CAAA;EACD,IAAA,QAAQlC,IAAI;EACR,MAAA,KAAK,QAAQ;UACT,OAAOG,iBAAiB,EAAE,CAAA;EAC9B,MAAA,KAAK,QAAQ;UACT,OAAOgB,iBAAiB,EAAE,CAAA;EAClC,KAAA;EACA;EACJ,GAAC,EAAE,CAACnB,IAAI,EAAEpB,aAAa,CAAC,CAAC,CAAA;EAC7B,CAAC,CAAA;AACD,MAAMuD,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAAC,KAAA,EAAsC;EAAA,EAAA,IAAhCnC,OAAO,GAAAmC,KAAA,CAAPnC,OAAO;MAAED,IAAI,GAAAoC,KAAA,CAAJpC,IAAI;MAAEqC,SAAS,GAAAD,KAAA,CAATC,SAAS,CAAA;EACtD,EAAA,IAAQzD,aAAa,GAAkCyD,SAAS,CAAxDzD,aAAa;MAAEL,IAAI,GAA4B8D,SAAS,CAAzC9D,IAAI;MAAEI,qBAAqB,GAAK0D,SAAS,CAAnC1D,qBAAqB,CAAA;EAClDkB,EAAAA,oBAAoB,CAAC;EACjBI,IAAAA,OAAO,EAAPA,OAAO;EACPrB,IAAAA,aAAa,EAAbA,aAAa;EACboB,IAAAA,IAAI,EAAJA,IAAI;EACJD,IAAAA,UAAU,EAAEpB,qBAAAA;EAChB,GAAC,CAAC,CAAA;EACF,EAAA,OAAOJ,IAAI,CAAA;EACf;;;AC3KA,MAAM+D,MAAM,GAAG,SAATA,MAAMA,CAAIhJ,KAAK,EAAK;EACtB,EAAA,IAAIkC,EAAE,EAAE+G,EAAE,EAAEC,EAAE,CAAA;EACd,EAAA,IAAQjJ,KAAK,GAAoCD,KAAK,CAA9CC,KAAK;MAAEkJ,aAAa,GAAqBnJ,KAAK,CAAvCmJ,aAAa;EAAKC,IAAAA,WAAW,GAAA9H,wBAAA,CAAKtB,KAAK,EAAAuB,SAAA,CAAA,CAAA;EACtD;EACJ;EACA;EACI,EAAA,IAAA8H,UAAA,GAA+LtJ,SAAS,CAACqJ,WAAW,EAAEnJ,KAAK,CAAC;MAApNgF,IAAI,GAAAoE,UAAA,CAAJpE,IAAI;MAAEhD,IAAI,GAAAoH,UAAA,CAAJpH,IAAI;MAAEG,IAAI,GAAAiH,UAAA,CAAJjH,IAAI;MAAEC,KAAK,GAAAgH,UAAA,CAALhH,KAAK;MAAEC,QAAQ,GAAA+G,UAAA,CAAR/G,QAAQ;MAAEK,WAAW,GAAA0G,UAAA,CAAX1G,WAAW;MAAEH,WAAW,GAAA6G,UAAA,CAAX7G,WAAW;MAAEI,YAAY,GAAAyG,UAAA,CAAZzG,YAAY;MAAEE,YAAY,GAAAuG,UAAA,CAAZvG,YAAY;MAAEG,WAAW,GAAAoG,UAAA,CAAXpG,WAAW;MAAEE,WAAW,GAAAkG,UAAA,CAAXlG,WAAW;MAAEE,OAAO,GAAAgG,UAAA,CAAPhG,OAAO;MAAEgC,qBAAqB,GAAAgE,UAAA,CAArBhE,qBAAqB;MAAEzD,eAAe,GAAAyH,UAAA,CAAfzH,eAAe;MAAE0D,aAAa,GAAA+D,UAAA,CAAb/D,aAAa,CAAA;EACzL;EACJ;EACA;EACIvB,EAAAA,eAAS,CAAC,YAAM;MACZ,IAAI/D,KAAK,CAACgB,SAAS,EAAE;EACjBhB,MAAAA,KAAK,CAACgB,SAAS,CAACmB,OAAO,GAAG;EACtBF,QAAAA,IAAI,EAAJA,IAAI;EACJG,QAAAA,IAAI,EAAJA,IAAI;EACJC,QAAAA,KAAK,EAALA,KAAK;EACLC,QAAAA,QAAQ,EAARA,QAAQ;EACRE,QAAAA,WAAW,EAAXA,WAAW;EACXG,QAAAA,WAAW,EAAXA,WAAW;EACXC,QAAAA,YAAY,EAAZA,YAAY;EACZE,QAAAA,YAAY,EAAZA,YAAY;EACZG,QAAAA,WAAW,EAAXA,WAAW;EACXE,QAAAA,WAAW,EAAXA,WAAW;EACXE,QAAAA,OAAO,EAAPA,OAAO;EACPgC,QAAAA,qBAAqB,EAArBA,qBAAqB;EACrBzD,QAAAA,eAAe,EAAfA,eAAe;EACf0D,QAAAA,aAAa,EAAbA,aAAAA;SACH,CAAA;EACL,KAAA;EACA;KACH,EAAE,CAAC,CAACpD,EAAE,GAAGlC,KAAK,CAACgB,SAAS,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,OAAO,CAAC,CAAC,CAAA;EAC5E,EAAA,OAAO0G,sBAAsB,CAAC;EAC1BE,IAAAA,SAAS,EAAE;EACP9D,MAAAA,IAAI,EAAJA,IAAI;EACJhD,MAAAA,IAAI,EAAJA,IAAI;EACJG,MAAAA,IAAI,EAAJA,IAAI;EACJC,MAAAA,KAAK,EAALA,KAAK;EACLC,MAAAA,QAAQ,EAARA,QAAQ;EACRK,MAAAA,WAAW,EAAXA,WAAW;EACXH,MAAAA,WAAW,EAAXA,WAAW;EACXI,MAAAA,YAAY,EAAZA,YAAY;EACZE,MAAAA,YAAY,EAAZA,YAAY;EACZG,MAAAA,WAAW,EAAXA,WAAW;EACXE,MAAAA,WAAW,EAAXA,WAAW;EACXE,MAAAA,OAAO,EAAPA,OAAO;EACPgC,MAAAA,qBAAqB,EAArBA,qBAAqB;EACrBzD,MAAAA,eAAe,EAAfA,eAAe;EACf0D,MAAAA,aAAa,EAAbA,aAAAA;OACH;EACDqB,IAAAA,OAAO,EAAE,CAACsC,EAAE,GAAGE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACxC,OAAO,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;EACvIvC,IAAAA,IAAI,EAAE,CAACwC,EAAE,GAAGC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACzC,IAAI,MAAM,IAAI,IAAIwC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,QAAA;EACnI,GAAC,CAAC,CAAA;EACN;;;;;;;;;;;;;;;;"}