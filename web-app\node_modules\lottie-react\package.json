{"name": "lottie-react", "version": "2.4.1", "description": "<PERSON><PERSON> for React", "keywords": ["lottie", "react", "lottie react", "react lottie", "lottie web", "animation", "component", "hook"], "homepage": "https://lottiereact.com", "bugs": {"url": "https://github.com/Gamote/lottie-react/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Gamote/lottie-react.git"}, "license": "MIT", "author": "<PERSON>", "main": "build/index.js", "module": "build/index.es.js", "browser": "build/index.umd.js", "types": "build/index.d.ts", "style": "build/index.css", "files": ["/build"], "scripts": {"build": "run-s tsc:compile rollup:compile", "postbuild": "npm pack && tar -xvzf *.tgz && rm -rf package *.tgz", "build:watch": "run-p tsc:compile:watch rollup:compile:watch", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls", "docz:build": "docz build", "deploy:docs": "echo 'lottiereact.com' > ./docs-dist/CNAME && gh-pages -d docs-dist", "docz:dev": "docz dev", "docz:serve": "docz build && docz serve", "prepublishOnly": "rm -rf build && yarn build", "rollup:compile": "rollup -c", "rollup:compile:watch": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "tsc:compile": "tsc", "tsc:compile:watch": "tsc --watch"}, "dependencies": {"lottie-web": "^5.10.2"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/preset-env": "^7.16.8", "@babel/preset-react": "^7.16.7", "@jest/types": "^27.4.2", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.1.3", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/react-hooks": "^7.0.2", "@types/jest": "^27.4.0", "@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "autoprefixer": "^10.4.2", "babel-loader": "^8.2.3", "coveralls": "^3.1.1", "docz": "^2.3.1", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^4.6.0", "get-pkg-repo": "^5.0.0", "gh-pages": "^3.2.3", "jest": "^27.4.7", "jest-canvas-mock": "^2.3.1", "sass": "^1.83.4", "npm-run-all": "4.1.5", "prettier": "^2.8.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-test-renderer": "^17.0.2", "rollup": "^2.64.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-dts": "^4.1.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^27.1.3", "ts-node": "^10.9.1", "tslib": "^2.5.0", "typescript": "^4.9.5"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}