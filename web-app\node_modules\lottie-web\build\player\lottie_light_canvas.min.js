"undefined"!=typeof document&&"undefined"!=typeof navigator&&function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).lottie=e()}(this,(function(){"use strict";var t="",e=!1,s=-999999,i=function(){return t};function a(t){return document.createElement(t)}function r(t,e){var s,i,a=t.length;for(s=0;s<a;s+=1)for(var r in i=t[s].prototype)Object.prototype.hasOwnProperty.call(i,r)&&(e.prototype[r]=i[r])}function n(t){function e(){}return e.prototype=t,e}var o=function(){function t(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(t){this.audios.push(t)},pause:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].pause()},resume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].resume()},setRate:function(t){var e,s=this.audios.length;for(e=0;e<s;e+=1)this.audios[e].setRate(t)},createAudio:function(t){return this.audioFactory?this.audioFactory(t):window.Howl?new window.Howl({src:[t]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(t){this.audioFactory=t},setVolume:function(t){this._volume=t,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].volume(this._volume*(this._isMuted?0:1))}},function(){return new t}}(),h=function(){function t(t,e){var s,i=0,a=[];switch(t){case"int16":case"uint8c":s=1;break;default:s=1.1}for(i=0;i<e;i+=1)a.push(s);return a}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?function(e,s){return"float32"===e?new Float32Array(s):"int16"===e?new Int16Array(s):"uint8c"===e?new Uint8ClampedArray(s):t(e,s)}:t}();function l(t){return Array.apply(null,{length:t})}function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}var f=!0,c=null,d=null,m="",u=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),g=Math.pow,y=Math.sqrt,v=Math.floor,b=(Math.max,Math.min),_={};!function(){var t,e=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],s=e.length;for(t=0;t<s;t+=1)_[e[t]]=Math[e[t]]}(),_.random=Math.random,_.abs=function(t){if("object"===p(t)&&t.length){var e,s=l(t.length),i=t.length;for(e=0;e<i;e+=1)s[e]=Math.abs(t[e]);return s}return Math.abs(t)};var C=150,x=Math.PI/180,k=.5519;function S(t){0}function P(t,e,s,i){this.type=t,this.currentTime=e,this.totalTime=s,this.direction=i<0?-1:1}function D(t,e){this.type=t,this.direction=e<0?-1:1}function w(t,e,s,i){this.type=t,this.currentLoop=s,this.totalLoops=e,this.direction=i<0?-1:1}function A(t,e,s){this.type=t,this.firstFrame=e,this.totalFrames=s}function M(t,e){this.type=t,this.target=e}function T(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function F(t){this.type="configError",this.nativeError=t}var E,L=(E=0,function(){return m+"__lottie_element_"+(E+=1)});function I(t,e,s){var i,a,r,n,o,h,l,p;switch(h=s*(1-e),l=s*(1-(o=6*t-(n=Math.floor(6*t)))*e),p=s*(1-(1-o)*e),n%6){case 0:i=s,a=p,r=h;break;case 1:i=l,a=s,r=h;break;case 2:i=h,a=s,r=p;break;case 3:i=h,a=l,r=s;break;case 4:i=p,a=h,r=s;break;case 5:i=s,a=h,r=l}return[i,a,r]}function R(t,e,s){var i,a=Math.max(t,e,s),r=Math.min(t,e,s),n=a-r,o=0===a?0:n/a,h=a/255;switch(a){case r:i=0;break;case t:i=e-s+n*(e<s?6:0),i/=6*n;break;case e:i=s-t+2*n,i/=6*n;break;case s:i=t-e+4*n,i/=6*n}return[i,o,h]}function V(t,e){var s=R(255*t[0],255*t[1],255*t[2]);return s[1]+=e,s[1]>1?s[1]=1:s[1]<=0&&(s[1]=0),I(s[0],s[1],s[2])}function z(t,e){var s=R(255*t[0],255*t[1],255*t[2]);return s[2]+=e,s[2]>1?s[2]=1:s[2]<0&&(s[2]=0),I(s[0],s[1],s[2])}function O(t,e){var s=R(255*t[0],255*t[1],255*t[2]);return s[0]+=e/360,s[0]>1?s[0]-=1:s[0]<0&&(s[0]+=1),I(s[0],s[1],s[2])}!function(){var t,e,s=[];for(t=0;t<256;t+=1)e=t.toString(16),s[t]=1===e.length?"0"+e:e}();var N=function(){return c},q=function(){return d},B=function(t){C=t},W=function(){return C};function j(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function Y(t){return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Y(t)}var J=function(){var t,s,i=1,a=[],r={onmessage:function(){},postMessage:function(e){t({data:e})}},n={postMessage:function(t){r.onmessage({data:t})}};function o(s){if(window.Worker&&window.Blob&&e){var i=new Blob(["var _workerSelf = self; self.onmessage = ",s.toString()],{type:"text/javascript"}),a=URL.createObjectURL(i);return new Worker(a)}return t=s,r}function h(){s||(s=o((function(t){if(n.dataManager||(n.dataManager=function(){function t(a,r){var n,o,h,l,p,c,d=a.length;for(o=0;o<d;o+=1)if("ks"in(n=a[o])&&!n.completed){if(n.completed=!0,n.hasMask){var m=n.masksProperties;for(l=m.length,h=0;h<l;h+=1)if(m[h].pt.k.i)i(m[h].pt.k);else for(c=m[h].pt.k.length,p=0;p<c;p+=1)m[h].pt.k[p].s&&i(m[h].pt.k[p].s[0]),m[h].pt.k[p].e&&i(m[h].pt.k[p].e[0])}0===n.ty?(n.layers=e(n.refId,r),t(n.layers,r)):4===n.ty?s(n.shapes):5===n.ty&&f(n)}}function e(t,e){var s=function(t,e){for(var s=0,i=e.length;s<i;){if(e[s].id===t)return e[s];s+=1}return null}(t,e);return s?s.layers.__used?JSON.parse(JSON.stringify(s.layers)):(s.layers.__used=!0,s.layers):null}function s(t){var e,a,r;for(e=t.length-1;e>=0;e-=1)if("sh"===t[e].ty)if(t[e].ks.k.i)i(t[e].ks.k);else for(r=t[e].ks.k.length,a=0;a<r;a+=1)t[e].ks.k[a].s&&i(t[e].ks.k[a].s[0]),t[e].ks.k[a].e&&i(t[e].ks.k[a].e[0]);else"gr"===t[e].ty&&s(t[e].it)}function i(t){var e,s=t.i.length;for(e=0;e<s;e+=1)t.i[e][0]+=t.v[e][0],t.i[e][1]+=t.v[e][1],t.o[e][0]+=t.v[e][0],t.o[e][1]+=t.v[e][1]}function a(t,e){var s=e?e.split("."):[100,100,100];return t[0]>s[0]||!(s[0]>t[0])&&(t[1]>s[1]||!(s[1]>t[1])&&(t[2]>s[2]||!(s[2]>t[2])&&null))}var r,n=function(){var t=[4,4,14];function e(t){var e,s,i,a=t.length;for(e=0;e<a;e+=1)5===t[e].ty&&(i=void 0,i=(s=t[e]).t.d,s.t.d={k:[{s:i,t:0}]})}return function(s){if(a(t,s.v)&&(e(s.layers),s.assets)){var i,r=s.assets.length;for(i=0;i<r;i+=1)s.assets[i].layers&&e(s.assets[i].layers)}}}(),o=(r=[4,7,99],function(t){if(t.chars&&!a(r,t.v)){var e,i=t.chars.length;for(e=0;e<i;e+=1){var n=t.chars[e];n.data&&n.data.shapes&&(s(n.data.shapes),n.data.ip=0,n.data.op=99999,n.data.st=0,n.data.sr=1,n.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},t.chars[e].t||(n.data.shapes.push({ty:"no"}),n.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}),h=function(){var t=[5,7,15];function e(t){var e,s,i=t.length;for(e=0;e<i;e+=1)5===t[e].ty&&(s=void 0,"number"==typeof(s=t[e].t.p).a&&(s.a={a:0,k:s.a}),"number"==typeof s.p&&(s.p={a:0,k:s.p}),"number"==typeof s.r&&(s.r={a:0,k:s.r}))}return function(s){if(a(t,s.v)&&(e(s.layers),s.assets)){var i,r=s.assets.length;for(i=0;i<r;i+=1)s.assets[i].layers&&e(s.assets[i].layers)}}}(),l=function(){var t=[4,1,9];function e(t){var s,i,a,r=t.length;for(s=0;s<r;s+=1)if("gr"===t[s].ty)e(t[s].it);else if("fl"===t[s].ty||"st"===t[s].ty)if(t[s].c.k&&t[s].c.k[0].i)for(a=t[s].c.k.length,i=0;i<a;i+=1)t[s].c.k[i].s&&(t[s].c.k[i].s[0]/=255,t[s].c.k[i].s[1]/=255,t[s].c.k[i].s[2]/=255,t[s].c.k[i].s[3]/=255),t[s].c.k[i].e&&(t[s].c.k[i].e[0]/=255,t[s].c.k[i].e[1]/=255,t[s].c.k[i].e[2]/=255,t[s].c.k[i].e[3]/=255);else t[s].c.k[0]/=255,t[s].c.k[1]/=255,t[s].c.k[2]/=255,t[s].c.k[3]/=255}function s(t){var s,i=t.length;for(s=0;s<i;s+=1)4===t[s].ty&&e(t[s].shapes)}return function(e){if(a(t,e.v)&&(s(e.layers),e.assets)){var i,r=e.assets.length;for(i=0;i<r;i+=1)e.assets[i].layers&&s(e.assets[i].layers)}}}(),p=function(){var t=[4,4,18];function e(t){var s,i,a;for(s=t.length-1;s>=0;s-=1)if("sh"===t[s].ty)if(t[s].ks.k.i)t[s].ks.k.c=t[s].closed;else for(a=t[s].ks.k.length,i=0;i<a;i+=1)t[s].ks.k[i].s&&(t[s].ks.k[i].s[0].c=t[s].closed),t[s].ks.k[i].e&&(t[s].ks.k[i].e[0].c=t[s].closed);else"gr"===t[s].ty&&e(t[s].it)}function s(t){var s,i,a,r,n,o,h=t.length;for(i=0;i<h;i+=1){if((s=t[i]).hasMask){var l=s.masksProperties;for(r=l.length,a=0;a<r;a+=1)if(l[a].pt.k.i)l[a].pt.k.c=l[a].cl;else for(o=l[a].pt.k.length,n=0;n<o;n+=1)l[a].pt.k[n].s&&(l[a].pt.k[n].s[0].c=l[a].cl),l[a].pt.k[n].e&&(l[a].pt.k[n].e[0].c=l[a].cl)}4===s.ty&&e(s.shapes)}}return function(e){if(a(t,e.v)&&(s(e.layers),e.assets)){var i,r=e.assets.length;for(i=0;i<r;i+=1)e.assets[i].layers&&s(e.assets[i].layers)}}}();function f(t){0===t.t.a.length&&t.t.p}var c={completeData:function(s){s.__complete||(l(s),n(s),o(s),h(s),p(s),t(s.layers,s.assets),function(s,i){if(s){var a=0,r=s.length;for(a=0;a<r;a+=1)1===s[a].t&&(s[a].data.layers=e(s[a].data.refId,i),t(s[a].data.layers,i))}}(s.chars,s.assets),s.__complete=!0)}};return c.checkColors=l,c.checkChars=o,c.checkPathProperties=h,c.checkShapes=p,c.completeLayers=t,c}()),n.assetLoader||(n.assetLoader=function(){function t(t){var e=t.getResponseHeader("content-type");return e&&"json"===t.responseType&&-1!==e.indexOf("json")||t.response&&"object"===Y(t.response)?t.response:t.response&&"string"==typeof t.response?JSON.parse(t.response):t.responseText?JSON.parse(t.responseText):null}return{load:function(e,s,i,a){var r,n=new XMLHttpRequest;try{n.responseType="json"}catch(t){}n.onreadystatechange=function(){if(4===n.readyState)if(200===n.status)r=t(n),i(r);else try{r=t(n),i(r)}catch(t){a&&a(t)}};try{n.open(["G","E","T"].join(""),e,!0)}catch(t){n.open(["G","E","T"].join(""),s+"/"+e,!0)}n.send()}}}()),"loadAnimation"===t.data.type)n.assetLoader.load(t.data.path,t.data.fullPath,(function(e){n.dataManager.completeData(e),n.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){n.postMessage({id:t.data.id,status:"error"})}));else if("complete"===t.data.type){var e=t.data.animation;n.dataManager.completeData(e),n.postMessage({id:t.data.id,payload:e,status:"success"})}else"loadData"===t.data.type&&n.assetLoader.load(t.data.path,t.data.fullPath,(function(e){n.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){n.postMessage({id:t.data.id,status:"error"})}))})),s.onmessage=function(t){var e=t.data,s=e.id,i=a[s];a[s]=null,"success"===e.status?i.onComplete(e.payload):i.onError&&i.onError()})}function l(t,e){var s="processId_"+(i+=1);return a[s]={onComplete:t,onError:e},s}return{loadAnimation:function(t,e,i){h();var a=l(e,i);s.postMessage({type:"loadAnimation",path:t,fullPath:window.location.origin+window.location.pathname,id:a})},loadData:function(t,e,i){h();var a=l(e,i);s.postMessage({type:"loadData",path:t,fullPath:window.location.origin+window.location.pathname,id:a})},completeAnimation:function(t,e,i){h();var a=l(e,i);s.postMessage({type:"complete",animation:t,id:a})}}}(),H=function(){var t=function(){var t=a("canvas");t.width=1,t.height=1;var e=t.getContext("2d");return e.fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1),t}();function e(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function s(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(t,e,s){var i="";if(t.e)i=t.p;else if(e){var a=t.p;-1!==a.indexOf("images/")&&(a=a.split("/")[1]),i=e+a}else i=s,i+=t.u?t.u:"",i+=t.p;return i}function r(t){var e=0,s=setInterval(function(){(t.getBBox().width||e>500)&&(this._imageLoaded(),clearInterval(s)),e+=1}.bind(this),50)}function n(t){var e={assetData:t},s=i(t,this.assetsPath,this.path);return J.loadData(s,function(t){e.img=t,this._footageLoaded()}.bind(this),function(){e.img={},this._footageLoaded()}.bind(this)),e}function o(){this._imageLoaded=e.bind(this),this._footageLoaded=s.bind(this),this.testImageLoaded=r.bind(this),this.createFootageData=n.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return o.prototype={loadAssets:function(t,e){var s;this.imagesLoadedCb=e;var i=t.length;for(s=0;s<i;s+=1)t[s].layers||(t[s].t&&"seq"!==t[s].t?3===t[s].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(t[s]))):(this.totalImages+=1,this.images.push(this._createImageData(t[s]))))},setAssetsPath:function(t){this.assetsPath=t||""},setPath:function(t){this.path=t||""},loadedImages:function(){return this.totalImages===this.loadedAssets},loadedFootages:function(){return this.totalFootages===this.loadedFootagesCount},destroy:function(){this.imagesLoadedCb=null,this.images.length=0},getAsset:function(t){for(var e=0,s=this.images.length;e<s;){if(this.images[e].assetData===t)return this.images[e].img;e+=1}return null},createImgData:function(e){var s=i(e,this.assetsPath,this.path),r=a("img");r.crossOrigin="anonymous",r.addEventListener("load",this._imageLoaded,!1),r.addEventListener("error",function(){n.img=t,this._imageLoaded()}.bind(this),!1),r.src=s;var n={img:r,assetData:e};return n},createImageData:function(e){var s=i(e,this.assetsPath,this.path),a=j("image");u?this.testImageLoaded(a):a.addEventListener("load",this._imageLoaded,!1),a.addEventListener("error",function(){r.img=t,this._imageLoaded()}.bind(this),!1),a.setAttributeNS("http://www.w3.org/1999/xlink","href",s),this._elementHelper.append?this._elementHelper.append(a):this._elementHelper.appendChild(a);var r={img:a,assetData:e};return r},imageLoaded:e,footageLoaded:s,setCacheType:function(t,e){"svg"===t?(this._elementHelper=e,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}},o}();function G(){}G.prototype={triggerEvent:function(t,e){if(this._cbs[t])for(var s=this._cbs[t],i=0;i<s.length;i+=1)s[i](e)},addEventListener:function(t,e){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(e),function(){this.removeEventListener(t,e)}.bind(this)},removeEventListener:function(t,e){if(e){if(this._cbs[t]){for(var s=0,i=this._cbs[t].length;s<i;)this._cbs[t][s]===e&&(this._cbs[t].splice(s,1),s-=1,i-=1),s+=1;this._cbs[t].length||(this._cbs[t]=null)}}else this._cbs[t]=null}};var X=function(){function t(t){for(var e,s=t.split("\r\n"),i={},a=0,r=0;r<s.length;r+=1)2===(e=s[r].split(":")).length&&(i[e[0]]=e[1].trim(),a+=1);if(0===a)throw new Error;return i}return function(e){for(var s=[],i=0;i<e.length;i+=1){var a=e[i],r={time:a.tm,duration:a.dr};try{r.payload=JSON.parse(e[i].cm)}catch(s){try{r.payload=t(e[i].cm)}catch(t){r.payload={name:e[i].cm}}}s.push(r)}return s}}(),K=function(){function t(t){this.compositions.push(t)}return function(){function e(t){for(var e=0,s=this.compositions.length;e<s;){if(this.compositions[e].data&&this.compositions[e].data.nm===t)return this.compositions[e].prepareFrame&&this.compositions[e].data.xt&&this.compositions[e].prepareFrame(this.currentFrame),this.compositions[e].compInterface;e+=1}return null}return e.compositions=[],e.currentFrame=0,e.registerComposition=t,e}}(),U={};function Z(t){return Z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Z(t)}var Q=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=L(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=f,this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=K(),this.imagePreloader=new H,this.audioController=o(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new P("drawnFrame",0,0,0),this.expressionsPlugin=N()};r([G],Q),Q.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg";t.animType?e=t.animType:t.renderer&&(e=t.renderer);var s=U[e];this.renderer=new s(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,""===t.loop||null===t.loop||void 0===t.loop||!0===t.loop?this.loop=!0:!1===t.loop?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay=!("autoplay"in t)||t.autoplay,this.name=t.name?t.name:"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(t,"autoloadSegments")||t.autoloadSegments,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(-1!==t.path.lastIndexOf("\\")?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),J.loadAnimation(t.path,this.configAnimation,this.onSetupError))},Q.prototype.onSetupError=function(){this.trigger("data_failed")},Q.prototype.setupAnimation=function(t){J.completeAnimation(t,this.configAnimation)},Q.prototype.setData=function(t,e){e&&"object"!==Z(e)&&(e=JSON.parse(e));var s={wrapper:t,animationData:e},i=t.attributes;s.path=i.getNamedItem("data-animation-path")?i.getNamedItem("data-animation-path").value:i.getNamedItem("data-bm-path")?i.getNamedItem("data-bm-path").value:i.getNamedItem("bm-path")?i.getNamedItem("bm-path").value:"",s.animType=i.getNamedItem("data-anim-type")?i.getNamedItem("data-anim-type").value:i.getNamedItem("data-bm-type")?i.getNamedItem("data-bm-type").value:i.getNamedItem("bm-type")?i.getNamedItem("bm-type").value:i.getNamedItem("data-bm-renderer")?i.getNamedItem("data-bm-renderer").value:i.getNamedItem("bm-renderer")?i.getNamedItem("bm-renderer").value:function(){if(U.canvas)return"canvas";for(var t in U)if(U[t])return t;return""}()||"canvas";var a=i.getNamedItem("data-anim-loop")?i.getNamedItem("data-anim-loop").value:i.getNamedItem("data-bm-loop")?i.getNamedItem("data-bm-loop").value:i.getNamedItem("bm-loop")?i.getNamedItem("bm-loop").value:"";"false"===a?s.loop=!1:"true"===a?s.loop=!0:""!==a&&(s.loop=parseInt(a,10));var r=i.getNamedItem("data-anim-autoplay")?i.getNamedItem("data-anim-autoplay").value:i.getNamedItem("data-bm-autoplay")?i.getNamedItem("data-bm-autoplay").value:!i.getNamedItem("bm-autoplay")||i.getNamedItem("bm-autoplay").value;s.autoplay="false"!==r,s.name=i.getNamedItem("data-name")?i.getNamedItem("data-name").value:i.getNamedItem("data-bm-name")?i.getNamedItem("data-bm-name").value:i.getNamedItem("bm-name")?i.getNamedItem("bm-name").value:"","false"===(i.getNamedItem("data-anim-prerender")?i.getNamedItem("data-anim-prerender").value:i.getNamedItem("data-bm-prerender")?i.getNamedItem("data-bm-prerender").value:i.getNamedItem("bm-prerender")?i.getNamedItem("bm-prerender").value:"")&&(s.prerender=!1),s.path?this.setParams(s):this.trigger("destroy")},Q.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));var e,s,i=this.animationData.layers,a=i.length,r=t.layers,n=r.length;for(s=0;s<n;s+=1)for(e=0;e<a;){if(i[e].id===r[s].id){i[e]=r[s];break}e+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(a=t.assets.length,e=0;e<a;e+=1)this.animationData.assets.push(t.assets[e]);this.animationData.__complete=!1,J.completeAnimation(this.animationData,this.onSegmentComplete)},Q.prototype.onSegmentComplete=function(t){this.animationData=t;var e=N();e&&e.initExpressions(this),this.loadNextSegment()},Q.prototype.loadNextSegment=function(){var t=this.animationData.segments;if(!t||0===t.length||!this.autoloadSegments)return this.trigger("data_ready"),void(this.timeCompleted=this.totalFrames);var e=t.shift();this.timeCompleted=e.time*this.frameRate;var s=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,J.loadData(s,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))},Q.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},Q.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},Q.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},Q.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=X(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},Q.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},Q.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var t=N();t&&t.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},Q.prototype.resize=function(t,e){var s="number"==typeof t?t:void 0,i="number"==typeof e?e:void 0;this.renderer.updateContainerSize(s,i)},Q.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},Q.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},Q.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.expressionsPlugin&&this.expressionsPlugin.resetFrame(),this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},Q.prototype.play=function(t){t&&this.name!==t||!0===this.isPaused&&(this.isPaused=!1,this.trigger("_play"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},Q.prototype.pause=function(t){t&&this.name!==t||!1===this.isPaused&&(this.isPaused=!0,this.trigger("_pause"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},Q.prototype.togglePause=function(t){t&&this.name!==t||(!0===this.isPaused?this.play():this.pause())},Q.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},Q.prototype.getMarkerData=function(t){for(var e,s=0;s<this.markers.length;s+=1)if((e=this.markers[s]).payload&&e.payload.name===t)return e;return null},Q.prototype.goToAndStop=function(t,e,s){if(!s||this.name===s){var i=Number(t);if(isNaN(i)){var a=this.getMarkerData(t);a&&this.goToAndStop(a.time,!0)}else e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier);this.pause()}},Q.prototype.goToAndPlay=function(t,e,s){if(!s||this.name===s){var i=Number(t);if(isNaN(i)){var a=this.getMarkerData(t);a&&(a.duration?this.playSegments([a.time,a.time+a.duration],!0):this.goToAndStop(a.time,!0))}else this.goToAndStop(i,e,s);this.play()}},Q.prototype.advanceTime=function(t){if(!0!==this.isPaused&&!1!==this.isLoaded){var e=this.currentRawFrame+t*this.frameModifier,s=!1;e>=this.totalFrames-1&&this.frameModifier>0?this.loop&&this.playCount!==this.loop?e>=this.totalFrames?(this.playCount+=1,this.checkSegments(e%this.totalFrames)||(this.setCurrentRawFrameValue(e%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(e):this.checkSegments(e>this.totalFrames?e%this.totalFrames:0)||(s=!0,e=this.totalFrames-1):e<0?this.checkSegments(e%this.totalFrames)||(!this.loop||this.playCount--<=0&&!0!==this.loop?(s=!0,e=0):(this.setCurrentRawFrameValue(this.totalFrames+e%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0)):this.setCurrentRawFrameValue(e),s&&(this.setCurrentRawFrameValue(e),this.pause(),this.trigger("complete"))}},Q.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},Q.prototype.setSegment=function(t,e){var s=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?s=t:this.currentRawFrame+this.firstFrame>e&&(s=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,-1!==s&&this.goToAndStop(s,!0)},Q.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),"object"===Z(t[0])){var s,i=t.length;for(s=0;s<i;s+=1)this.segments.push(t[s])}else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},Q.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},Q.prototype.checkSegments=function(t){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),t),!0)},Q.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.expressionsPlugin=null,this.imagePreloader=null,this.projectInterface=null)},Q.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},Q.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},Q.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},Q.prototype.setLoop=function(t){this.loop=t},Q.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},Q.prototype.getVolume=function(){return this.audioController.getVolume()},Q.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},Q.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},Q.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},Q.prototype.getPath=function(){return this.path},Q.prototype.getAssetsPath=function(t){var e="";if(t.e)e=t.p;else if(this.assetsPath){var s=t.p;-1!==s.indexOf("images/")&&(s=s.split("/")[1]),e=this.assetsPath+s}else e=this.path,e+=t.u?t.u:"",e+=t.p;return e},Q.prototype.getAssetData=function(t){for(var e=0,s=this.assets.length;e<s;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},Q.prototype.hide=function(){this.renderer.hide()},Q.prototype.show=function(){this.renderer.show()},Q.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},Q.prototype.updateDocumentData=function(t,e,s){try{this.renderer.getElementByPath(t).updateDocumentData(e,s)}catch(t){}},Q.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new P(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new w(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new D(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new A(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new M(t,this));break;default:this.triggerEvent(t)}"enterFrame"===t&&this.onEnterFrame&&this.onEnterFrame.call(this,new P(t,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===t&&this.onLoopComplete&&this.onLoopComplete.call(this,new w(t,this.loop,this.playCount,this.frameMult)),"complete"===t&&this.onComplete&&this.onComplete.call(this,new D(t,this.frameMult)),"segmentStart"===t&&this.onSegmentStart&&this.onSegmentStart.call(this,new A(t,this.firstFrame,this.totalFrames)),"destroy"===t&&this.onDestroy&&this.onDestroy.call(this,new M(t,this))},Q.prototype.triggerRenderFrameError=function(t){var e=new T(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)},Q.prototype.triggerConfigError=function(t){var e=new F(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)};var $=function(){var t={},e=[],s=0,i=0,r=0,n=!0,o=!1;function h(t){for(var s=0,a=t.target;s<i;)e[s].animation===a&&(e.splice(s,1),s-=1,i-=1,a.isPaused||f()),s+=1}function l(t,s){if(!t)return null;for(var a=0;a<i;){if(e[a].elem===t&&null!==e[a].elem)return e[a].animation;a+=1}var r=new Q;return c(r,t),r.setData(t,s),r}function p(){r+=1,u()}function f(){r-=1}function c(t,s){t.addEventListener("destroy",h),t.addEventListener("_active",p),t.addEventListener("_idle",f),e.push({elem:s,animation:t}),i+=1}function d(t){var a,h=t-s;for(a=0;a<i;a+=1)e[a].animation.advanceTime(h);s=t,r&&!o?window.requestAnimationFrame(d):n=!0}function m(t){s=t,window.requestAnimationFrame(d)}function u(){!o&&r&&n&&(window.requestAnimationFrame(m),n=!1)}return t.registerAnimation=l,t.loadAnimation=function(t){var e=new Q;return c(e,null),e.setParams(t),e},t.setSpeed=function(t,s){var a;for(a=0;a<i;a+=1)e[a].animation.setSpeed(t,s)},t.setDirection=function(t,s){var a;for(a=0;a<i;a+=1)e[a].animation.setDirection(t,s)},t.play=function(t){var s;for(s=0;s<i;s+=1)e[s].animation.play(t)},t.pause=function(t){var s;for(s=0;s<i;s+=1)e[s].animation.pause(t)},t.stop=function(t){var s;for(s=0;s<i;s+=1)e[s].animation.stop(t)},t.togglePause=function(t){var s;for(s=0;s<i;s+=1)e[s].animation.togglePause(t)},t.searchAnimations=function(t,e,s){var i,r=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),n=r.length;for(i=0;i<n;i+=1)s&&r[i].setAttribute("data-bm-type",s),l(r[i],t);if(e&&0===n){s||(s="svg");var o=document.getElementsByTagName("body")[0];o.innerText="";var h=a("div");h.style.width="100%",h.style.height="100%",h.setAttribute("data-bm-type",s),o.appendChild(h),l(h,t)}},t.resize=function(){var t;for(t=0;t<i;t+=1)e[t].animation.resize()},t.goToAndStop=function(t,s,a){var r;for(r=0;r<i;r+=1)e[r].animation.goToAndStop(t,s,a)},t.destroy=function(t){var s;for(s=i-1;s>=0;s-=1)e[s].animation.destroy(t)},t.freeze=function(){o=!0},t.unfreeze=function(){o=!1,u()},t.setVolume=function(t,s){var a;for(a=0;a<i;a+=1)e[a].animation.setVolume(t,s)},t.mute=function(t){var s;for(s=0;s<i;s+=1)e[s].animation.mute(t)},t.unmute=function(t){var s;for(s=0;s<i;s+=1)e[s].animation.unmute(t)},t.getRegisteredAnimations=function(){var t,s=e.length,i=[];for(t=0;t<s;t+=1)i.push(e[t].animation);return i},t}(),tt=function(){var t={getBezierEasing:function(t,s,i,a,r){var n=r||("bez_"+t+"_"+s+"_"+i+"_"+a).replace(/\./g,"p");if(e[n])return e[n];var o=new p([t,s,i,a]);return e[n]=o,o}},e={};var s=11,i=1/(s-1),a="function"==typeof Float32Array;function r(t,e){return 1-3*e+3*t}function n(t,e){return 3*e-6*t}function o(t){return 3*t}function h(t,e,s){return((r(e,s)*t+n(e,s))*t+o(e))*t}function l(t,e,s){return 3*r(e,s)*t*t+2*n(e,s)*t+o(e)}function p(t){this._p=t,this._mSampleValues=a?new Float32Array(s):new Array(s),this._precomputed=!1,this.get=this.get.bind(this)}return p.prototype={get:function(t){var e=this._p[0],s=this._p[1],i=this._p[2],a=this._p[3];return this._precomputed||this._precompute(),e===s&&i===a?t:0===t?0:1===t?1:h(this._getTForX(t),s,a)},_precompute:function(){var t=this._p[0],e=this._p[1],s=this._p[2],i=this._p[3];this._precomputed=!0,t===e&&s===i||this._calcSampleValues()},_calcSampleValues:function(){for(var t=this._p[0],e=this._p[2],a=0;a<s;++a)this._mSampleValues[a]=h(a*i,t,e)},_getTForX:function(t){for(var e=this._p[0],a=this._p[2],r=this._mSampleValues,n=0,o=1,p=s-1;o!==p&&r[o]<=t;++o)n+=i;var f=n+(t-r[--o])/(r[o+1]-r[o])*i,c=l(f,e,a);return c>=.001?function(t,e,s,i){for(var a=0;a<4;++a){var r=l(e,s,i);if(0===r)return e;e-=(h(e,s,i)-t)/r}return e}(t,f,e,a):0===c?f:function(t,e,s,i,a){var r,n,o=0;do{(r=h(n=e+(s-e)/2,i,a)-t)>0?s=n:e=n}while(Math.abs(r)>1e-7&&++o<10);return n}(t,n,n+i,e,a)}},t}(),et={double:function(t){return t.concat(l(t.length))}},st=function(t,e,s){var i=0,a=t,r=l(a);return{newElement:function(){return i?r[i-=1]:e()},release:function(t){i===a&&(r=et.double(r),a*=2),s&&s(t),r[i]=t,i+=1}}},it=st(8,(function(){return{addedLength:0,percents:h("float32",W()),lengths:h("float32",W())}})),at=st(8,(function(){return{lengths:[],totalLength:0}}),(function(t){var e,s=t.lengths.length;for(e=0;e<s;e+=1)it.release(t.lengths[e]);t.lengths.length=0}));var rt=function(){var t=Math;function e(t,e,s,i,a,r){var n=t*i+e*a+s*r-a*i-r*t-s*e;return n>-.001&&n<.001}var s=function(t,e,s,i){var a,r,n,o,h,l,p=W(),f=0,c=[],d=[],m=it.newElement();for(n=s.length,a=0;a<p;a+=1){for(h=a/(p-1),l=0,r=0;r<n;r+=1)o=g(1-h,3)*t[r]+3*g(1-h,2)*h*s[r]+3*(1-h)*g(h,2)*i[r]+g(h,3)*e[r],c[r]=o,null!==d[r]&&(l+=g(c[r]-d[r],2)),d[r]=c[r];l&&(f+=l=y(l)),m.percents[a]=h,m.lengths[a]=f}return m.addedLength=f,m};function i(t){this.segmentLength=0,this.points=new Array(t)}function a(t,e){this.partialLength=t,this.point=e}var r,n=(r={},function(t,s,n,o){var h=(t[0]+"_"+t[1]+"_"+s[0]+"_"+s[1]+"_"+n[0]+"_"+n[1]+"_"+o[0]+"_"+o[1]).replace(/\./g,"p");if(!r[h]){var p,f,c,d,m,u,v,b=W(),_=0,C=null;2===t.length&&(t[0]!==s[0]||t[1]!==s[1])&&e(t[0],t[1],s[0],s[1],t[0]+n[0],t[1]+n[1])&&e(t[0],t[1],s[0],s[1],s[0]+o[0],s[1]+o[1])&&(b=2);var x=new i(b);for(c=n.length,p=0;p<b;p+=1){for(v=l(c),m=p/(b-1),u=0,f=0;f<c;f+=1)d=g(1-m,3)*t[f]+3*g(1-m,2)*m*(t[f]+n[f])+3*(1-m)*g(m,2)*(s[f]+o[f])+g(m,3)*s[f],v[f]=d,null!==C&&(u+=g(v[f]-C[f],2));_+=u=y(u),x.points[p]=new a(u,v),C=v}x.segmentLength=_,r[h]=x}return r[h]});function o(t,e){var s=e.percents,i=e.lengths,a=s.length,r=v((a-1)*t),n=t*e.addedLength,o=0;if(r===a-1||0===r||n===i[r])return s[r];for(var h=i[r]>n?-1:1,l=!0;l;)if(i[r]<=n&&i[r+1]>n?(o=(n-i[r])/(i[r+1]-i[r]),l=!1):r+=h,r<0||r>=a-1){if(r===a-1)return s[r];l=!1}return s[r]+(s[r+1]-s[r])*o}var p=h("float32",8);return{getSegmentsLength:function(t){var e,i=at.newElement(),a=t.c,r=t.v,n=t.o,o=t.i,h=t._length,l=i.lengths,p=0;for(e=0;e<h-1;e+=1)l[e]=s(r[e],r[e+1],n[e],o[e+1]),p+=l[e].addedLength;return a&&h&&(l[e]=s(r[e],r[0],n[e],o[0]),p+=l[e].addedLength),i.totalLength=p,i},getNewSegment:function(e,s,i,a,r,n,h){r<0?r=0:r>1&&(r=1);var l,f=o(r,h),c=o(n=n>1?1:n,h),d=e.length,m=1-f,u=1-c,g=m*m*m,y=f*m*m*3,v=f*f*m*3,b=f*f*f,_=m*m*u,C=f*m*u+m*f*u+m*m*c,x=f*f*u+m*f*c+f*m*c,k=f*f*c,S=m*u*u,P=f*u*u+m*c*u+m*u*c,D=f*c*u+m*c*c+f*u*c,w=f*c*c,A=u*u*u,M=c*u*u+u*c*u+u*u*c,T=c*c*u+u*c*c+c*u*c,F=c*c*c;for(l=0;l<d;l+=1)p[4*l]=t.round(1e3*(g*e[l]+y*i[l]+v*a[l]+b*s[l]))/1e3,p[4*l+1]=t.round(1e3*(_*e[l]+C*i[l]+x*a[l]+k*s[l]))/1e3,p[4*l+2]=t.round(1e3*(S*e[l]+P*i[l]+D*a[l]+w*s[l]))/1e3,p[4*l+3]=t.round(1e3*(A*e[l]+M*i[l]+T*a[l]+F*s[l]))/1e3;return p},getPointInSegment:function(e,s,i,a,r,n){var h=o(r,n),l=1-h;return[t.round(1e3*(l*l*l*e[0]+(h*l*l+l*h*l+l*l*h)*i[0]+(h*h*l+l*h*h+h*l*h)*a[0]+h*h*h*s[0]))/1e3,t.round(1e3*(l*l*l*e[1]+(h*l*l+l*h*l+l*l*h)*i[1]+(h*h*l+l*h*h+h*l*h)*a[1]+h*h*h*s[1]))/1e3]},buildBezierData:n,pointOnLine2D:e,pointOnLine3D:function(s,i,a,r,n,o,h,l,p){if(0===a&&0===o&&0===p)return e(s,i,r,n,h,l);var f,c=t.sqrt(t.pow(r-s,2)+t.pow(n-i,2)+t.pow(o-a,2)),d=t.sqrt(t.pow(h-s,2)+t.pow(l-i,2)+t.pow(p-a,2)),m=t.sqrt(t.pow(h-r,2)+t.pow(l-n,2)+t.pow(p-o,2));return(f=c>d?c>m?c-d-m:m-d-c:m>d?m-d-c:d-c-m)>-1e-4&&f<1e-4}}}(),nt=s,ot=Math.abs;function ht(t,e){var s,i=this.offsetTime;"multidimensional"===this.propType&&(s=h("float32",this.pv.length));for(var a,r,n,o,l,p,f,c,d,m=e.lastIndex,u=m,g=this.keyframes.length-1,y=!0;y;){if(a=this.keyframes[u],r=this.keyframes[u+1],u===g-1&&t>=r.t-i){a.h&&(a=r),m=0;break}if(r.t-i>t){m=u;break}u<g-1?u+=1:(m=0,y=!1)}n=this.keyframesMetadata[u]||{};var v,b,_,C,k,S,P,D,w,A,M=r.t-i,T=a.t-i;if(a.to){n.bezierData||(n.bezierData=rt.buildBezierData(a.s,r.s||a.e,a.to,a.ti));var F=n.bezierData;if(t>=M||t<T){var E=t>=M?F.points.length-1:0;for(l=F.points[E].point.length,o=0;o<l;o+=1)s[o]=F.points[E].point[o]}else{n.__fnct?d=n.__fnct:(d=tt.getBezierEasing(a.o.x,a.o.y,a.i.x,a.i.y,a.n).get,n.__fnct=d),p=d((t-T)/(M-T));var L,I=F.segmentLength*p,R=e.lastFrame<t&&e._lastKeyframeIndex===u?e._lastAddedLength:0;for(c=e.lastFrame<t&&e._lastKeyframeIndex===u?e._lastPoint:0,y=!0,f=F.points.length;y;){if(R+=F.points[c].partialLength,0===I||0===p||c===F.points.length-1){for(l=F.points[c].point.length,o=0;o<l;o+=1)s[o]=F.points[c].point[o];break}if(I>=R&&I<R+F.points[c+1].partialLength){for(L=(I-R)/F.points[c+1].partialLength,l=F.points[c].point.length,o=0;o<l;o+=1)s[o]=F.points[c].point[o]+(F.points[c+1].point[o]-F.points[c].point[o])*L;break}c<f-1?c+=1:y=!1}e._lastPoint=c,e._lastAddedLength=R-F.points[c].partialLength,e._lastKeyframeIndex=u}}else{var V,z,O,N,q;if(g=a.s.length,v=r.s||a.e,this.sh&&1!==a.h)if(t>=M)s[0]=v[0],s[1]=v[1],s[2]=v[2];else if(t<=T)s[0]=a.s[0],s[1]=a.s[1],s[2]=a.s[2];else{var B=lt(a.s),W=lt(v);b=s,_=function(t,e,s){var i,a,r,n,o,h=[],l=t[0],p=t[1],f=t[2],c=t[3],d=e[0],m=e[1],u=e[2],g=e[3];return(a=l*d+p*m+f*u+c*g)<0&&(a=-a,d=-d,m=-m,u=-u,g=-g),1-a>1e-6?(i=Math.acos(a),r=Math.sin(i),n=Math.sin((1-s)*i)/r,o=Math.sin(s*i)/r):(n=1-s,o=s),h[0]=n*l+o*d,h[1]=n*p+o*m,h[2]=n*f+o*u,h[3]=n*c+o*g,h}(B,W,(t-T)/(M-T)),C=_[0],k=_[1],S=_[2],P=_[3],D=Math.atan2(2*k*P-2*C*S,1-2*k*k-2*S*S),w=Math.asin(2*C*k+2*S*P),A=Math.atan2(2*C*P-2*k*S,1-2*C*C-2*S*S),b[0]=D/x,b[1]=w/x,b[2]=A/x}else for(u=0;u<g;u+=1)1!==a.h&&(t>=M?p=1:t<T?p=0:(a.o.x.constructor===Array?(n.__fnct||(n.__fnct=[]),n.__fnct[u]?d=n.__fnct[u]:(V=void 0===a.o.x[u]?a.o.x[0]:a.o.x[u],z=void 0===a.o.y[u]?a.o.y[0]:a.o.y[u],O=void 0===a.i.x[u]?a.i.x[0]:a.i.x[u],N=void 0===a.i.y[u]?a.i.y[0]:a.i.y[u],d=tt.getBezierEasing(V,z,O,N).get,n.__fnct[u]=d)):n.__fnct?d=n.__fnct:(V=a.o.x,z=a.o.y,O=a.i.x,N=a.i.y,d=tt.getBezierEasing(V,z,O,N).get,a.keyframeMetadata=d),p=d((t-T)/(M-T)))),v=r.s||a.e,q=1===a.h?a.s[u]:a.s[u]+(v[u]-a.s[u])*p,"multidimensional"===this.propType?s[u]=q:s=q}return e.lastIndex=m,s}function lt(t){var e=t[0]*x,s=t[1]*x,i=t[2]*x,a=Math.cos(e/2),r=Math.cos(s/2),n=Math.cos(i/2),o=Math.sin(e/2),h=Math.sin(s/2),l=Math.sin(i/2);return[o*h*n+a*r*l,o*r*n+a*h*l,a*h*n-o*r*l,a*r*n-o*h*l]}function pt(){var t=this.comp.renderedFrame-this.offsetTime,e=this.keyframes[0].t-this.offsetTime,s=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(t===this._caching.lastFrame||this._caching.lastFrame!==nt&&(this._caching.lastFrame>=s&&t>=s||this._caching.lastFrame<e&&t<e))){this._caching.lastFrame>=t&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var i=this.interpolateValue(t,this._caching);this.pv=i}return this._caching.lastFrame=t,this.pv}function ft(t){var e;if("unidimensional"===this.propType)e=t*this.mult,ot(this.v-e)>1e-5&&(this.v=e,this._mdf=!0);else for(var s=0,i=this.v.length;s<i;)e=t[s]*this.mult,ot(this.v[s]-e)>1e-5&&(this.v[s]=e,this._mdf=!0),s+=1}function ct(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t;this.lock=!0,this._mdf=this._isFirstFrame;var e=this.effectsSequence.length,s=this.kf?this.pv:this.data.k;for(t=0;t<e;t+=1)s=this.effectsSequence[t](s);this.setVValue(s),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function dt(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function mt(t,e,s,i){this.propType="unidimensional",this.mult=s||1,this.data=e,this.v=s?e.k*s:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=i,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=ct,this.setVValue=ft,this.addEffect=dt}function ut(t,e,s,i){var a;this.propType="multidimensional",this.mult=s||1,this.data=e,this._mdf=!1,this.elem=t,this.container=i,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var r=e.k.length;for(this.v=h("float32",r),this.pv=h("float32",r),this.vel=h("float32",r),a=0;a<r;a+=1)this.v[a]=e.k[a]*this.mult,this.pv[a]=e.k[a];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=ct,this.setVValue=ft,this.addEffect=dt}function gt(t,e,s,i){this.propType="unidimensional",this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.frameId=-1,this._caching={lastFrame:nt,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=e,this.mult=s||1,this.elem=t,this.container=i,this.comp=t.comp,this.v=nt,this.pv=nt,this._isFirstFrame=!0,this.getValue=ct,this.setVValue=ft,this.interpolateValue=ht,this.effectsSequence=[pt.bind(this)],this.addEffect=dt}function yt(t,e,s,i){var a;this.propType="multidimensional";var r,n,o,l,p=e.k.length;for(a=0;a<p-1;a+=1)e.k[a].to&&e.k[a].s&&e.k[a+1]&&e.k[a+1].s&&(r=e.k[a].s,n=e.k[a+1].s,o=e.k[a].to,l=e.k[a].ti,(2===r.length&&(r[0]!==n[0]||r[1]!==n[1])&&rt.pointOnLine2D(r[0],r[1],n[0],n[1],r[0]+o[0],r[1]+o[1])&&rt.pointOnLine2D(r[0],r[1],n[0],n[1],n[0]+l[0],n[1]+l[1])||3===r.length&&(r[0]!==n[0]||r[1]!==n[1]||r[2]!==n[2])&&rt.pointOnLine3D(r[0],r[1],r[2],n[0],n[1],n[2],r[0]+o[0],r[1]+o[1],r[2]+o[2])&&rt.pointOnLine3D(r[0],r[1],r[2],n[0],n[1],n[2],n[0]+l[0],n[1]+l[1],n[2]+l[2]))&&(e.k[a].to=null,e.k[a].ti=null),r[0]===n[0]&&r[1]===n[1]&&0===o[0]&&0===o[1]&&0===l[0]&&0===l[1]&&(2===r.length||r[2]===n[2]&&0===o[2]&&0===l[2])&&(e.k[a].to=null,e.k[a].ti=null));this.effectsSequence=[pt.bind(this)],this.data=e,this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=s||1,this.elem=t,this.container=i,this.comp=t.comp,this.getValue=ct,this.setVValue=ft,this.interpolateValue=ht,this.frameId=-1;var f=e.k[0].s.length;for(this.v=h("float32",f),this.pv=h("float32",f),a=0;a<f;a+=1)this.v[a]=nt,this.pv[a]=nt;this._caching={lastFrame:nt,lastIndex:0,value:h("float32",f)},this.addEffect=dt}var vt={getProp:function(t,e,s,i,a){var r;if(e.sid&&(e=t.globalData.slotManager.getProp(e)),e.k.length)if("number"==typeof e.k[0])r=new ut(t,e,i,a);else switch(s){case 0:r=new gt(t,e,i,a);break;case 1:r=new yt(t,e,i,a)}else r=new mt(t,e,i,a);return r.effectsSequence.length&&a.addDynamicProperty(r),r}};function bt(){}bt.prototype={addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){var t;this._mdf=!1;var e=this.dynamicProperties.length;for(t=0;t<e;t+=1)this.dynamicProperties[t].getValue(),this.dynamicProperties[t]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var _t=st(8,(function(){return h("float32",2)}));function Ct(){this.c=!1,this._length=0,this._maxLength=8,this.v=l(this._maxLength),this.o=l(this._maxLength),this.i=l(this._maxLength)}Ct.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var s=0;s<e;)this.v[s]=_t.newElement(),this.o[s]=_t.newElement(),this.i[s]=_t.newElement(),s+=1},Ct.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},Ct.prototype.doubleArrayLength=function(){this.v=this.v.concat(l(this._maxLength)),this.i=this.i.concat(l(this._maxLength)),this.o=this.o.concat(l(this._maxLength)),this._maxLength*=2},Ct.prototype.setXYAt=function(t,e,s,i,a){var r;switch(this._length=Math.max(this._length,i+1),this._length>=this._maxLength&&this.doubleArrayLength(),s){case"v":r=this.v;break;case"i":r=this.i;break;case"o":r=this.o;break;default:r=[]}(!r[i]||r[i]&&!a)&&(r[i]=_t.newElement()),r[i][0]=t,r[i][1]=e},Ct.prototype.setTripleAt=function(t,e,s,i,a,r,n,o){this.setXYAt(t,e,"v",n,o),this.setXYAt(s,i,"o",n,o),this.setXYAt(a,r,"i",n,o)},Ct.prototype.reverse=function(){var t=new Ct;t.setPathData(this.c,this._length);var e=this.v,s=this.o,i=this.i,a=0;this.c&&(t.setTripleAt(e[0][0],e[0][1],i[0][0],i[0][1],s[0][0],s[0][1],0,!1),a=1);var r,n=this._length-1,o=this._length;for(r=a;r<o;r+=1)t.setTripleAt(e[n][0],e[n][1],i[n][0],i[n][1],s[n][0],s[n][1],r,!1),n-=1;return t},Ct.prototype.length=function(){return this._length};var xt,kt=((xt=st(4,(function(){return new Ct}),(function(t){var e,s=t._length;for(e=0;e<s;e+=1)_t.release(t.v[e]),_t.release(t.i[e]),_t.release(t.o[e]),t.v[e]=null,t.i[e]=null,t.o[e]=null;t._length=0,t.c=!1}))).clone=function(t){var e,s=xt.newElement(),i=void 0===t._length?t.v.length:t._length;for(s.setLength(i),s.c=t.c,e=0;e<i;e+=1)s.setTripleAt(t.v[e][0],t.v[e][1],t.o[e][0],t.o[e][1],t.i[e][0],t.i[e][1],e);return s},xt);function St(){this._length=0,this._maxLength=4,this.shapes=l(this._maxLength)}St.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(l(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},St.prototype.releaseShapes=function(){var t;for(t=0;t<this._length;t+=1)kt.release(this.shapes[t]);this._length=0};var Pt,Dt,wt,At,Mt=(Pt={newShapeCollection:function(){return Dt?At[Dt-=1]:new St},release:function(t){var e,s=t._length;for(e=0;e<s;e+=1)kt.release(t.shapes[e]);t._length=0,Dt===wt&&(At=et.double(At),wt*=2),At[Dt]=t,Dt+=1}},Dt=0,At=l(wt=4),Pt),Tt=function(){var t=-999999;function e(t,e,s){var i,a,r,n,o,h,l,p,f,c=s.lastIndex,d=this.keyframes;if(t<d[0].t-this.offsetTime)i=d[0].s[0],r=!0,c=0;else if(t>=d[d.length-1].t-this.offsetTime)i=d[d.length-1].s?d[d.length-1].s[0]:d[d.length-2].e[0],r=!0;else{for(var m,u,g,y=c,v=d.length-1,b=!0;b&&(m=d[y],!((u=d[y+1]).t-this.offsetTime>t));)y<v-1?y+=1:b=!1;if(g=this.keyframesMetadata[y]||{},c=y,!(r=1===m.h)){if(t>=u.t-this.offsetTime)p=1;else if(t<m.t-this.offsetTime)p=0;else{var _;g.__fnct?_=g.__fnct:(_=tt.getBezierEasing(m.o.x,m.o.y,m.i.x,m.i.y).get,g.__fnct=_),p=_((t-(m.t-this.offsetTime))/(u.t-this.offsetTime-(m.t-this.offsetTime)))}a=u.s?u.s[0]:m.e[0]}i=m.s[0]}for(h=e._length,l=i.i[0].length,s.lastIndex=c,n=0;n<h;n+=1)for(o=0;o<l;o+=1)f=r?i.i[n][o]:i.i[n][o]+(a.i[n][o]-i.i[n][o])*p,e.i[n][o]=f,f=r?i.o[n][o]:i.o[n][o]+(a.o[n][o]-i.o[n][o])*p,e.o[n][o]=f,f=r?i.v[n][o]:i.v[n][o]+(a.v[n][o]-i.v[n][o])*p,e.v[n][o]=f}function s(){var e=this.comp.renderedFrame-this.offsetTime,s=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime,a=this._caching.lastFrame;return a!==t&&(a<s&&e<s||a>i&&e>i)||(this._caching.lastIndex=a<e?this._caching.lastIndex:0,this.interpolateShape(e,this.pv,this._caching)),this._caching.lastFrame=e,this.pv}function i(){this.paths=this.localShapeCollection}function a(t){(function(t,e){if(t._length!==e._length||t.c!==e.c)return!1;var s,i=t._length;for(s=0;s<i;s+=1)if(t.v[s][0]!==e.v[s][0]||t.v[s][1]!==e.v[s][1]||t.o[s][0]!==e.o[s][0]||t.o[s][1]!==e.o[s][1]||t.i[s][0]!==e.i[s][0]||t.i[s][1]!==e.i[s][1])return!1;return!0})(this.v,t)||(this.v=kt.clone(t),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function n(){if(this.elem.globalData.frameId!==this.frameId)if(this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t,e;this.lock=!0,this._mdf=!1,t=this.kf?this.pv:this.data.ks?this.data.ks.k:this.data.pt.k;var s=this.effectsSequence.length;for(e=0;e<s;e+=1)t=this.effectsSequence[e](t);this.setVValue(t),this.lock=!1,this.frameId=this.elem.globalData.frameId}else this._mdf=!1}function o(t,e,s){this.propType="shape",this.comp=t.comp,this.container=t,this.elem=t,this.data=e,this.k=!1,this.kf=!1,this._mdf=!1;var a=3===s?e.pt.k:e.ks.k;this.v=kt.clone(a),this.pv=kt.clone(this.v),this.localShapeCollection=Mt.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=i,this.effectsSequence=[]}function h(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function l(e,a,r){this.propType="shape",this.comp=e.comp,this.elem=e,this.container=e,this.offsetTime=e.data.st,this.keyframes=3===r?a.pt.k:a.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var n=this.keyframes[0].s[0].i.length;this.v=kt.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,n),this.pv=kt.clone(this.v),this.localShapeCollection=Mt.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=t,this.reset=i,this._caching={lastFrame:t,lastIndex:0},this.effectsSequence=[s.bind(this)]}o.prototype.interpolateShape=e,o.prototype.getValue=n,o.prototype.setVValue=a,o.prototype.addEffect=h,l.prototype.getValue=n,l.prototype.interpolateShape=e,l.prototype.setVValue=a,l.prototype.addEffect=h;var p=function(){var t=k;function e(t,e){this.v=kt.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=Mt.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=e.d,this.elem=t,this.comp=t.comp,this.frameId=-1,this.initDynamicPropertyContainer(t),this.p=vt.getProp(t,e.p,1,0,this),this.s=vt.getProp(t,e.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return e.prototype={reset:i,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var e=this.p.v[0],s=this.p.v[1],i=this.s.v[0]/2,a=this.s.v[1]/2,r=3!==this.d,n=this.v;n.v[0][0]=e,n.v[0][1]=s-a,n.v[1][0]=r?e+i:e-i,n.v[1][1]=s,n.v[2][0]=e,n.v[2][1]=s+a,n.v[3][0]=r?e-i:e+i,n.v[3][1]=s,n.i[0][0]=r?e-i*t:e+i*t,n.i[0][1]=s-a,n.i[1][0]=r?e+i:e-i,n.i[1][1]=s-a*t,n.i[2][0]=r?e+i*t:e-i*t,n.i[2][1]=s+a,n.i[3][0]=r?e-i:e+i,n.i[3][1]=s+a*t,n.o[0][0]=r?e+i*t:e-i*t,n.o[0][1]=s-a,n.o[1][0]=r?e+i:e-i,n.o[1][1]=s+a*t,n.o[2][0]=r?e-i*t:e+i*t,n.o[2][1]=s+a,n.o[3][0]=r?e-i:e+i,n.o[3][1]=s-a*t}},r([bt],e),e}(),f=function(){function t(t,e){this.v=kt.newElement(),this.v.setPathData(!0,0),this.elem=t,this.comp=t.comp,this.data=e,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),1===e.sy?(this.ir=vt.getProp(t,e.ir,0,0,this),this.is=vt.getProp(t,e.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=vt.getProp(t,e.pt,0,0,this),this.p=vt.getProp(t,e.p,1,0,this),this.r=vt.getProp(t,e.r,0,x,this),this.or=vt.getProp(t,e.or,0,0,this),this.os=vt.getProp(t,e.os,0,.01,this),this.localShapeCollection=Mt.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return t.prototype={reset:i,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var t,e,s,i,a=2*Math.floor(this.pt.v),r=2*Math.PI/a,n=!0,o=this.or.v,h=this.ir.v,l=this.os.v,p=this.is.v,f=2*Math.PI*o/(2*a),c=2*Math.PI*h/(2*a),d=-Math.PI/2;d+=this.r.v;var m=3===this.data.d?-1:1;for(this.v._length=0,t=0;t<a;t+=1){s=n?l:p,i=n?f:c;var u=(e=n?o:h)*Math.cos(d),g=e*Math.sin(d),y=0===u&&0===g?0:g/Math.sqrt(u*u+g*g),v=0===u&&0===g?0:-u/Math.sqrt(u*u+g*g);u+=+this.p.v[0],g+=+this.p.v[1],this.v.setTripleAt(u,g,u-y*i*s*m,g-v*i*s*m,u+y*i*s*m,g+v*i*s*m,t,!0),n=!n,d+=r*m}},convertPolygonToPath:function(){var t,e=Math.floor(this.pt.v),s=2*Math.PI/e,i=this.or.v,a=this.os.v,r=2*Math.PI*i/(4*e),n=.5*-Math.PI,o=3===this.data.d?-1:1;for(n+=this.r.v,this.v._length=0,t=0;t<e;t+=1){var h=i*Math.cos(n),l=i*Math.sin(n),p=0===h&&0===l?0:l/Math.sqrt(h*h+l*l),f=0===h&&0===l?0:-h/Math.sqrt(h*h+l*l);h+=+this.p.v[0],l+=+this.p.v[1],this.v.setTripleAt(h,l,h-p*r*a*o,l-f*r*a*o,h+p*r*a*o,l+f*r*a*o,t,!0),n+=s*o}this.paths.length=0,this.paths[0]=this.v}},r([bt],t),t}(),c=function(){function t(t,e){this.v=kt.newElement(),this.v.c=!0,this.localShapeCollection=Mt.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=t,this.comp=t.comp,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),this.p=vt.getProp(t,e.p,1,0,this),this.s=vt.getProp(t,e.s,1,0,this),this.r=vt.getProp(t,e.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return t.prototype={convertRectToPath:function(){var t=this.p.v[0],e=this.p.v[1],s=this.s.v[0]/2,i=this.s.v[1]/2,a=b(s,i,this.r.v),r=a*(1-k);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(t+s,e-i+a,t+s,e-i+a,t+s,e-i+r,0,!0),this.v.setTripleAt(t+s,e+i-a,t+s,e+i-r,t+s,e+i-a,1,!0),0!==a?(this.v.setTripleAt(t+s-a,e+i,t+s-a,e+i,t+s-r,e+i,2,!0),this.v.setTripleAt(t-s+a,e+i,t-s+r,e+i,t-s+a,e+i,3,!0),this.v.setTripleAt(t-s,e+i-a,t-s,e+i-a,t-s,e+i-r,4,!0),this.v.setTripleAt(t-s,e-i+a,t-s,e-i+r,t-s,e-i+a,5,!0),this.v.setTripleAt(t-s+a,e-i,t-s+a,e-i,t-s+r,e-i,6,!0),this.v.setTripleAt(t+s-a,e-i,t+s-r,e-i,t+s-a,e-i,7,!0)):(this.v.setTripleAt(t-s,e+i,t-s+r,e+i,t-s,e+i,2),this.v.setTripleAt(t-s,e-i,t-s,e-i+r,t-s,e-i,3))):(this.v.setTripleAt(t+s,e-i+a,t+s,e-i+r,t+s,e-i+a,0,!0),0!==a?(this.v.setTripleAt(t+s-a,e-i,t+s-a,e-i,t+s-r,e-i,1,!0),this.v.setTripleAt(t-s+a,e-i,t-s+r,e-i,t-s+a,e-i,2,!0),this.v.setTripleAt(t-s,e-i+a,t-s,e-i+a,t-s,e-i+r,3,!0),this.v.setTripleAt(t-s,e+i-a,t-s,e+i-r,t-s,e+i-a,4,!0),this.v.setTripleAt(t-s+a,e+i,t-s+a,e+i,t-s+r,e+i,5,!0),this.v.setTripleAt(t+s-a,e+i,t+s-r,e+i,t+s-a,e+i,6,!0),this.v.setTripleAt(t+s,e+i-a,t+s,e+i-a,t+s,e+i-r,7,!0)):(this.v.setTripleAt(t-s,e-i,t-s+r,e-i,t-s,e-i,1,!0),this.v.setTripleAt(t-s,e+i,t-s,e+i-r,t-s,e+i,2,!0),this.v.setTripleAt(t+s,e+i,t+s-r,e+i,t+s,e+i,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:i},r([bt],t),t}();var d={getShapeProp:function(t,e,s){var i;return 3===s||4===s?i=(3===s?e.pt:e.ks).k.length?new l(t,e,s):new o(t,e,s):5===s?i=new c(t,e):6===s?i=new p(t,e):7===s&&(i=new f(t,e)),i.k&&t.addDynamicProperty(i),i},getConstructorFunction:function(){return o},getKeyframedConstructorFunction:function(){return l}};return d}(),Ft=function(){var t=Math.cos,e=Math.sin,s=Math.tan,i=Math.round;function a(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function r(s){if(0===s)return this;var i=t(s),a=e(s);return this._t(i,-a,0,0,a,i,0,0,0,0,1,0,0,0,0,1)}function n(s){if(0===s)return this;var i=t(s),a=e(s);return this._t(1,0,0,0,0,i,-a,0,0,a,i,0,0,0,0,1)}function o(s){if(0===s)return this;var i=t(s),a=e(s);return this._t(i,0,a,0,0,1,0,0,-a,0,i,0,0,0,0,1)}function l(s){if(0===s)return this;var i=t(s),a=e(s);return this._t(i,-a,0,0,a,i,0,0,0,0,1,0,0,0,0,1)}function p(t,e){return this._t(1,e,t,1,0,0)}function f(t,e){return this.shear(s(t),s(e))}function c(i,a){var r=t(a),n=e(a);return this._t(r,n,0,0,-n,r,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,s(i),1,0,0,0,0,1,0,0,0,0,1)._t(r,-n,0,0,n,r,0,0,0,0,1,0,0,0,0,1)}function d(t,e,s){return s||0===s||(s=1),1===t&&1===e&&1===s?this:this._t(t,0,0,0,0,e,0,0,0,0,s,0,0,0,0,1)}function m(t,e,s,i,a,r,n,o,h,l,p,f,c,d,m,u){return this.props[0]=t,this.props[1]=e,this.props[2]=s,this.props[3]=i,this.props[4]=a,this.props[5]=r,this.props[6]=n,this.props[7]=o,this.props[8]=h,this.props[9]=l,this.props[10]=p,this.props[11]=f,this.props[12]=c,this.props[13]=d,this.props[14]=m,this.props[15]=u,this}function u(t,e,s){return s=s||0,0!==t||0!==e||0!==s?this._t(1,0,0,0,0,1,0,0,0,0,1,0,t,e,s,1):this}function g(t,e,s,i,a,r,n,o,h,l,p,f,c,d,m,u){var g=this.props;if(1===t&&0===e&&0===s&&0===i&&0===a&&1===r&&0===n&&0===o&&0===h&&0===l&&1===p&&0===f)return g[12]=g[12]*t+g[15]*c,g[13]=g[13]*r+g[15]*d,g[14]=g[14]*p+g[15]*m,g[15]*=u,this._identityCalculated=!1,this;var y=g[0],v=g[1],b=g[2],_=g[3],C=g[4],x=g[5],k=g[6],S=g[7],P=g[8],D=g[9],w=g[10],A=g[11],M=g[12],T=g[13],F=g[14],E=g[15];return g[0]=y*t+v*a+b*h+_*c,g[1]=y*e+v*r+b*l+_*d,g[2]=y*s+v*n+b*p+_*m,g[3]=y*i+v*o+b*f+_*u,g[4]=C*t+x*a+k*h+S*c,g[5]=C*e+x*r+k*l+S*d,g[6]=C*s+x*n+k*p+S*m,g[7]=C*i+x*o+k*f+S*u,g[8]=P*t+D*a+w*h+A*c,g[9]=P*e+D*r+w*l+A*d,g[10]=P*s+D*n+w*p+A*m,g[11]=P*i+D*o+w*f+A*u,g[12]=M*t+T*a+F*h+E*c,g[13]=M*e+T*r+F*l+E*d,g[14]=M*s+T*n+F*p+E*m,g[15]=M*i+T*o+F*f+E*u,this._identityCalculated=!1,this}function y(t){var e=t.props;return this.transform(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}function v(){return this._identityCalculated||(this._identity=!(1!==this.props[0]||0!==this.props[1]||0!==this.props[2]||0!==this.props[3]||0!==this.props[4]||1!==this.props[5]||0!==this.props[6]||0!==this.props[7]||0!==this.props[8]||0!==this.props[9]||1!==this.props[10]||0!==this.props[11]||0!==this.props[12]||0!==this.props[13]||0!==this.props[14]||1!==this.props[15]),this._identityCalculated=!0),this._identity}function b(t){for(var e=0;e<16;){if(t.props[e]!==this.props[e])return!1;e+=1}return!0}function _(t){var e;for(e=0;e<16;e+=1)t.props[e]=this.props[e];return t}function C(t){var e;for(e=0;e<16;e+=1)this.props[e]=t[e]}function x(t,e,s){return{x:t*this.props[0]+e*this.props[4]+s*this.props[8]+this.props[12],y:t*this.props[1]+e*this.props[5]+s*this.props[9]+this.props[13],z:t*this.props[2]+e*this.props[6]+s*this.props[10]+this.props[14]}}function k(t,e,s){return t*this.props[0]+e*this.props[4]+s*this.props[8]+this.props[12]}function S(t,e,s){return t*this.props[1]+e*this.props[5]+s*this.props[9]+this.props[13]}function P(t,e,s){return t*this.props[2]+e*this.props[6]+s*this.props[10]+this.props[14]}function D(){var t=this.props[0]*this.props[5]-this.props[1]*this.props[4],e=this.props[5]/t,s=-this.props[1]/t,i=-this.props[4]/t,a=this.props[0]/t,r=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/t,n=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/t,o=new Ft;return o.props[0]=e,o.props[1]=s,o.props[4]=i,o.props[5]=a,o.props[12]=r,o.props[13]=n,o}function w(t){return this.getInverseMatrix().applyToPointArray(t[0],t[1],t[2]||0)}function A(t){var e,s=t.length,i=[];for(e=0;e<s;e+=1)i[e]=w(t[e]);return i}function M(t,e,s){var i=h("float32",6);if(this.isIdentity())i[0]=t[0],i[1]=t[1],i[2]=e[0],i[3]=e[1],i[4]=s[0],i[5]=s[1];else{var a=this.props[0],r=this.props[1],n=this.props[4],o=this.props[5],l=this.props[12],p=this.props[13];i[0]=t[0]*a+t[1]*n+l,i[1]=t[0]*r+t[1]*o+p,i[2]=e[0]*a+e[1]*n+l,i[3]=e[0]*r+e[1]*o+p,i[4]=s[0]*a+s[1]*n+l,i[5]=s[0]*r+s[1]*o+p}return i}function T(t,e,s){return this.isIdentity()?[t,e,s]:[t*this.props[0]+e*this.props[4]+s*this.props[8]+this.props[12],t*this.props[1]+e*this.props[5]+s*this.props[9]+this.props[13],t*this.props[2]+e*this.props[6]+s*this.props[10]+this.props[14]]}function F(t,e){if(this.isIdentity())return t+","+e;var s=this.props;return Math.round(100*(t*s[0]+e*s[4]+s[12]))/100+","+Math.round(100*(t*s[1]+e*s[5]+s[13]))/100}function E(){for(var t=0,e=this.props,s="matrix3d(";t<16;)s+=i(1e4*e[t])/1e4,s+=15===t?")":",",t+=1;return s}function L(t){return t<1e-6&&t>0||t>-1e-6&&t<0?i(1e4*t)/1e4:t}function I(){var t=this.props;return"matrix("+L(t[0])+","+L(t[1])+","+L(t[4])+","+L(t[5])+","+L(t[12])+","+L(t[13])+")"}return function(){this.reset=a,this.rotate=r,this.rotateX=n,this.rotateY=o,this.rotateZ=l,this.skew=f,this.skewFromAxis=c,this.shear=p,this.scale=d,this.setTransform=m,this.translate=u,this.transform=g,this.multiply=y,this.applyToPoint=x,this.applyToX=k,this.applyToY=S,this.applyToZ=P,this.applyToPointArray=T,this.applyToTriplePoints=M,this.applyToPointStringified=F,this.toCSS=E,this.to2dCSS=I,this.clone=_,this.cloneFromProps=C,this.equals=b,this.inversePoints=A,this.inversePoint=w,this.getInverseMatrix=D,this._t=this.transform,this.isIdentity=v,this._identity=!0,this._identityCalculated=!1,this.props=h("float32",16),this.reset()}}();function Et(t){return Et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Et(t)}var Lt={},It="__[STANDALONE]__";function Rt(){$.searchAnimations()}Lt.play=$.play,Lt.pause=$.pause,Lt.setLocationHref=function(e){t=e},Lt.togglePause=$.togglePause,Lt.setSpeed=$.setSpeed,Lt.setDirection=$.setDirection,Lt.stop=$.stop,Lt.searchAnimations=Rt,Lt.registerAnimation=$.registerAnimation,Lt.loadAnimation=function(t){return $.loadAnimation(t)},Lt.setSubframeRendering=function(t){!function(t){f=!!t}(t)},Lt.resize=$.resize,Lt.goToAndStop=$.goToAndStop,Lt.destroy=$.destroy,Lt.setQuality=function(t){if("string"==typeof t)switch(t){case"high":B(200);break;default:case"medium":B(50);break;case"low":B(10)}else!isNaN(t)&&t>1&&B(t);W()>=50?S(!1):S(!0)},Lt.inBrowser=function(){return"undefined"!=typeof navigator},Lt.installPlugin=function(t,e){"expressions"===t&&(c=e)},Lt.freeze=$.freeze,Lt.unfreeze=$.unfreeze,Lt.setVolume=$.setVolume,Lt.mute=$.mute,Lt.unmute=$.unmute,Lt.getRegisteredAnimations=$.getRegisteredAnimations,Lt.useWebWorker=function(t){e=!!t},Lt.setIDPrefix=function(t){m=t},Lt.__getFactory=function(t){switch(t){case"propertyFactory":return vt;case"shapePropertyFactory":return Tt;case"matrix":return Ft;default:return null}},Lt.version="5.13.0";var Vt="";if(It){var zt=document.getElementsByTagName("script"),Ot=zt[zt.length-1]||{src:""};Vt=Ot.src?Ot.src.replace(/^[^\?]+\??/,""):"",function(t){for(var e=Vt.split("&"),s=0;s<e.length;s+=1){var i=e[s].split("=");if(decodeURIComponent(i[0])==t)return decodeURIComponent(i[1])}return null}("renderer")}var Nt=setInterval((function(){"complete"===document.readyState&&(clearInterval(Nt),Rt())}),100);try{"object"===("undefined"==typeof exports?"undefined":Et(exports))&&"undefined"!=typeof module||"function"==typeof define&&define.amd||(window.bodymovin=Lt)}catch(t){}var qt=function(){var t={},e={};return t.registerModifier=function(t,s){e[t]||(e[t]=s)},t.getModifier=function(t,s,i){return new e[t](s,i)},t}();function Bt(){}function Wt(){}function jt(){}Bt.prototype.initModifierProperties=function(){},Bt.prototype.addShapeToModifier=function(){},Bt.prototype.addShape=function(t){if(!this.closed){t.sh.container.addDynamicProperty(t.sh);var e={shape:t.sh,data:t,localShapeCollection:Mt.newShapeCollection()};this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated()}},Bt.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=s,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},Bt.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},r([bt],Bt),r([Bt],Wt),Wt.prototype.initModifierProperties=function(t,e){this.s=vt.getProp(t,e.s,0,.01,this),this.e=vt.getProp(t,e.e,0,.01,this),this.o=vt.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},Wt.prototype.addShapeToModifier=function(t){t.pathsData=[]},Wt.prototype.calculateShapeEdges=function(t,e,s,i,a){var r=[];e<=1?r.push({s:t,e:e}):t>=1?r.push({s:t-1,e:e-1}):(r.push({s:t,e:1}),r.push({s:0,e:e-1}));var n,o,h=[],l=r.length;for(n=0;n<l;n+=1){var p,f;if(!((o=r[n]).e*a<i||o.s*a>i+s))p=o.s*a<=i?0:(o.s*a-i)/s,f=o.e*a>=i+s?1:(o.e*a-i)/s,h.push([p,f])}return h.length||h.push([0,0]),h},Wt.prototype.releasePathsData=function(t){var e,s=t.length;for(e=0;e<s;e+=1)at.release(t[e]);return t.length=0,t},Wt.prototype.processShapes=function(t){var e,s,i,a;if(this._mdf||t){var r=this.o.v%360/360;if(r<0&&(r+=1),(e=this.s.v>1?1+r:this.s.v<0?0+r:this.s.v+r)>(s=this.e.v>1?1+r:this.e.v<0?0+r:this.e.v+r)){var n=e;e=s,s=n}e=1e-4*Math.round(1e4*e),s=1e-4*Math.round(1e4*s),this.sValue=e,this.eValue=s}else e=this.sValue,s=this.eValue;var o,h,l,p,f,c=this.shapes.length,d=0;if(s===e)for(a=0;a<c;a+=1)this.shapes[a].localShapeCollection.releaseShapes(),this.shapes[a].shape._mdf=!0,this.shapes[a].shape.paths=this.shapes[a].localShapeCollection,this._mdf&&(this.shapes[a].pathsData.length=0);else if(1===s&&0===e||0===s&&1===e){if(this._mdf)for(a=0;a<c;a+=1)this.shapes[a].pathsData.length=0,this.shapes[a].shape._mdf=!0}else{var m,u,g=[];for(a=0;a<c;a+=1)if((m=this.shapes[a]).shape._mdf||this._mdf||t||2===this.m){if(h=(i=m.shape.paths)._length,f=0,!m.shape._mdf&&m.pathsData.length)f=m.totalShapeLength;else{for(l=this.releasePathsData(m.pathsData),o=0;o<h;o+=1)p=rt.getSegmentsLength(i.shapes[o]),l.push(p),f+=p.totalLength;m.totalShapeLength=f,m.pathsData=l}d+=f,m.shape._mdf=!0}else m.shape.paths=m.localShapeCollection;var y,v=e,b=s,_=0;for(a=c-1;a>=0;a-=1)if((m=this.shapes[a]).shape._mdf){for((u=m.localShapeCollection).releaseShapes(),2===this.m&&c>1?(y=this.calculateShapeEdges(e,s,m.totalShapeLength,_,d),_+=m.totalShapeLength):y=[[v,b]],h=y.length,o=0;o<h;o+=1){v=y[o][0],b=y[o][1],g.length=0,b<=1?g.push({s:m.totalShapeLength*v,e:m.totalShapeLength*b}):v>=1?g.push({s:m.totalShapeLength*(v-1),e:m.totalShapeLength*(b-1)}):(g.push({s:m.totalShapeLength*v,e:m.totalShapeLength}),g.push({s:0,e:m.totalShapeLength*(b-1)}));var C=this.addShapes(m,g[0]);if(g[0].s!==g[0].e){if(g.length>1)if(m.shape.paths.shapes[m.shape.paths._length-1].c){var x=C.pop();this.addPaths(C,u),C=this.addShapes(m,g[1],x)}else this.addPaths(C,u),C=this.addShapes(m,g[1]);this.addPaths(C,u)}}m.shape.paths=u}}},Wt.prototype.addPaths=function(t,e){var s,i=t.length;for(s=0;s<i;s+=1)e.addShape(t[s])},Wt.prototype.addSegment=function(t,e,s,i,a,r,n){a.setXYAt(e[0],e[1],"o",r),a.setXYAt(s[0],s[1],"i",r+1),n&&a.setXYAt(t[0],t[1],"v",r),a.setXYAt(i[0],i[1],"v",r+1)},Wt.prototype.addSegmentFromArray=function(t,e,s,i){e.setXYAt(t[1],t[5],"o",s),e.setXYAt(t[2],t[6],"i",s+1),i&&e.setXYAt(t[0],t[4],"v",s),e.setXYAt(t[3],t[7],"v",s+1)},Wt.prototype.addShapes=function(t,e,s){var i,a,r,n,o,h,l,p,f=t.pathsData,c=t.shape.paths.shapes,d=t.shape.paths._length,m=0,u=[],g=!0;for(s?(o=s._length,p=s._length):(s=kt.newElement(),o=0,p=0),u.push(s),i=0;i<d;i+=1){for(h=f[i].lengths,s.c=c[i].c,r=c[i].c?h.length:h.length+1,a=1;a<r;a+=1)if(m+(n=h[a-1]).addedLength<e.s)m+=n.addedLength,s.c=!1;else{if(m>e.e){s.c=!1;break}e.s<=m&&e.e>=m+n.addedLength?(this.addSegment(c[i].v[a-1],c[i].o[a-1],c[i].i[a],c[i].v[a],s,o,g),g=!1):(l=rt.getNewSegment(c[i].v[a-1],c[i].v[a],c[i].o[a-1],c[i].i[a],(e.s-m)/n.addedLength,(e.e-m)/n.addedLength,h[a-1]),this.addSegmentFromArray(l,s,o,g),g=!1,s.c=!1),m+=n.addedLength,o+=1}if(c[i].c&&h.length){if(n=h[a-1],m<=e.e){var y=h[a-1].addedLength;e.s<=m&&e.e>=m+y?(this.addSegment(c[i].v[a-1],c[i].o[a-1],c[i].i[0],c[i].v[0],s,o,g),g=!1):(l=rt.getNewSegment(c[i].v[a-1],c[i].v[0],c[i].o[a-1],c[i].i[0],(e.s-m)/y,(e.e-m)/y,h[a-1]),this.addSegmentFromArray(l,s,o,g),g=!1,s.c=!1)}else s.c=!1;m+=n.addedLength,o+=1}if(s._length&&(s.setXYAt(s.v[p][0],s.v[p][1],"i",p),s.setXYAt(s.v[s._length-1][0],s.v[s._length-1][1],"o",s._length-1)),m>e.e)break;i<d-1&&(s=kt.newElement(),g=!0,u.push(s),o=0)}return u},r([Bt],jt),jt.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=vt.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},jt.prototype.processPath=function(t,e){var s=e/100,i=[0,0],a=t._length,r=0;for(r=0;r<a;r+=1)i[0]+=t.v[r][0],i[1]+=t.v[r][1];i[0]/=a,i[1]/=a;var n,o,h,l,p,f,c=kt.newElement();for(c.c=t.c,r=0;r<a;r+=1)n=t.v[r][0]+(i[0]-t.v[r][0])*s,o=t.v[r][1]+(i[1]-t.v[r][1])*s,h=t.o[r][0]+(i[0]-t.o[r][0])*-s,l=t.o[r][1]+(i[1]-t.o[r][1])*-s,p=t.i[r][0]+(i[0]-t.i[r][0])*-s,f=t.i[r][1]+(i[1]-t.i[r][1])*-s,c.setTripleAt(n,o,h,l,p,f,r);return c},jt.prototype.processShapes=function(t){var e,s,i,a,r,n,o=this.shapes.length,h=this.amount.v;if(0!==h)for(s=0;s<o;s+=1){if(n=(r=this.shapes[s]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,i=0;i<a;i+=1)n.addShape(this.processPath(e[i],h));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var Yt=function(){var t=[0,0];function e(t,e,s){if(this.elem=t,this.frameId=-1,this.propType="transform",this.data=e,this.v=new Ft,this.pre=new Ft,this.appliedTransformations=0,this.initDynamicPropertyContainer(s||t),e.p&&e.p.s?(this.px=vt.getProp(t,e.p.x,0,0,this),this.py=vt.getProp(t,e.p.y,0,0,this),e.p.z&&(this.pz=vt.getProp(t,e.p.z,0,0,this))):this.p=vt.getProp(t,e.p||{k:[0,0,0]},1,0,this),e.rx){if(this.rx=vt.getProp(t,e.rx,0,x,this),this.ry=vt.getProp(t,e.ry,0,x,this),this.rz=vt.getProp(t,e.rz,0,x,this),e.or.k[0].ti){var i,a=e.or.k.length;for(i=0;i<a;i+=1)e.or.k[i].to=null,e.or.k[i].ti=null}this.or=vt.getProp(t,e.or,1,x,this),this.or.sh=!0}else this.r=vt.getProp(t,e.r||{k:0},0,x,this);e.sk&&(this.sk=vt.getProp(t,e.sk,0,x,this),this.sa=vt.getProp(t,e.sa,0,x,this)),this.a=vt.getProp(t,e.a||{k:[0,0,0]},1,0,this),this.s=vt.getProp(t,e.s||{k:[100,100,100]},1,.01,this),e.o?this.o=vt.getProp(t,e.o,0,.01,t):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return e.prototype={applyToMatrix:function(t){var e=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||e,this.a&&t.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&t.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&t.skewFromAxis(-this.sk.v,this.sa.v),this.r?t.rotate(-this.r.v):t.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?t.translate(this.px.v,this.py.v,-this.pz.v):t.translate(this.px.v,this.py.v,0):t.translate(this.p.v[0],this.p.v[1],-this.p.v[2])},getValue:function(e){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||e){var s;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){var i,a;if(s=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(i=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/s,0),a=this.p.getValueAtTime(this.p.keyframes[0].t/s,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(i=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/s,0),a=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/s,0)):(i=this.p.pv,a=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/s,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){i=[],a=[];var r=this.px,n=this.py;r._caching.lastFrame+r.offsetTime<=r.keyframes[0].t?(i[0]=r.getValueAtTime((r.keyframes[0].t+.01)/s,0),i[1]=n.getValueAtTime((n.keyframes[0].t+.01)/s,0),a[0]=r.getValueAtTime(r.keyframes[0].t/s,0),a[1]=n.getValueAtTime(n.keyframes[0].t/s,0)):r._caching.lastFrame+r.offsetTime>=r.keyframes[r.keyframes.length-1].t?(i[0]=r.getValueAtTime(r.keyframes[r.keyframes.length-1].t/s,0),i[1]=n.getValueAtTime(n.keyframes[n.keyframes.length-1].t/s,0),a[0]=r.getValueAtTime((r.keyframes[r.keyframes.length-1].t-.01)/s,0),a[1]=n.getValueAtTime((n.keyframes[n.keyframes.length-1].t-.01)/s,0)):(i=[r.pv,n.pv],a[0]=r.getValueAtTime((r._caching.lastFrame+r.offsetTime-.01)/s,r.offsetTime),a[1]=n.getValueAtTime((n._caching.lastFrame+n.offsetTime-.01)/s,n.offsetTime))}else i=a=t;this.v.rotate(-Math.atan2(i[1]-a[1],i[0]-a[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}},precalculateMatrix:function(){if(this.appliedTransformations=0,this.pre.reset(),!this.a.effectsSequence.length&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}},autoOrient:function(){}},r([bt],e),e.prototype.addDynamicProperty=function(t){this._addDynamicProperty(t),this.elem.addDynamicProperty(t),this._isDirty=!0},e.prototype._addDynamicProperty=bt.prototype.addDynamicProperty,{getTransformProperty:function(t,s,i){return new e(t,s,i)}}}();function Jt(){}function Ht(){}function Gt(t,e){return 1e5*Math.abs(t-e)<=Math.min(Math.abs(t),Math.abs(e))}function Xt(t){return Math.abs(t)<=1e-5}function Kt(t,e,s){return t*(1-s)+e*s}function Ut(t,e,s){return[Kt(t[0],e[0],s),Kt(t[1],e[1],s)]}function Zt(t,e,s,i){return[3*e-t-3*s+i,3*t-6*e+3*s,-3*t+3*e,t]}function Qt(t){return new $t(t,t,t,t,!1)}function $t(t,e,s,i,a){a&&he(t,e)&&(e=Ut(t,i,1/3)),a&&he(s,i)&&(s=Ut(t,i,2/3));var r=Zt(t[0],e[0],s[0],i[0]),n=Zt(t[1],e[1],s[1],i[1]);this.a=[r[0],n[0]],this.b=[r[1],n[1]],this.c=[r[2],n[2]],this.d=[r[3],n[3]],this.points=[t,e,s,i]}function te(t,e){var s=t.points[0][e],i=t.points[t.points.length-1][e];if(s>i){var a=i;i=s,s=a}for(var r=function(t,e,s){if(0===t)return[];var i=e*e-4*t*s;if(i<0)return[];var a=-e/(2*t);if(0===i)return[a];var r=Math.sqrt(i)/(2*t);return[a-r,a+r]}(3*t.a[e],2*t.b[e],t.c[e]),n=0;n<r.length;n+=1)if(r[n]>0&&r[n]<1){var o=t.point(r[n])[e];o<s?s=o:o>i&&(i=o)}return{min:s,max:i}}function ee(t,e,s){var i=t.boundingBox();return{cx:i.cx,cy:i.cy,width:i.width,height:i.height,bez:t,t:(e+s)/2,t1:e,t2:s}}function se(t){var e=t.bez.split(.5);return[ee(e[0],t.t1,t.t),ee(e[1],t.t,t.t2)]}function ie(t,e,s,i,a,r){var n,o;if(n=t,o=e,2*Math.abs(n.cx-o.cx)<n.width+o.width&&2*Math.abs(n.cy-o.cy)<n.height+o.height)if(s>=r||t.width<=i&&t.height<=i&&e.width<=i&&e.height<=i)a.push([t.t,e.t]);else{var h=se(t),l=se(e);ie(h[0],l[0],s+1,i,a,r),ie(h[0],l[1],s+1,i,a,r),ie(h[1],l[0],s+1,i,a,r),ie(h[1],l[1],s+1,i,a,r)}}function ae(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function re(t,e,s,i){var a=[t[0],t[1],1],r=[e[0],e[1],1],n=[s[0],s[1],1],o=[i[0],i[1],1],h=ae(ae(a,r),ae(n,o));return Xt(h[2])?null:[h[0]/h[2],h[1]/h[2]]}function ne(t,e,s){return[t[0]+Math.cos(e)*s,t[1]-Math.sin(e)*s]}function oe(t,e){return Math.hypot(t[0]-e[0],t[1]-e[1])}function he(t,e){return Gt(t[0],e[0])&&Gt(t[1],e[1])}function le(){}function pe(t,e,s,i,a,r,n){var o=s-Math.PI/2,h=s+Math.PI/2,l=e[0]+Math.cos(s)*i*a,p=e[1]-Math.sin(s)*i*a;t.setTripleAt(l,p,l+Math.cos(o)*r,p-Math.sin(o)*r,l+Math.cos(h)*n,p-Math.sin(h)*n,t.length())}function fe(t,e){var s,i,a,r,n=0===e?t.length()-1:e-1,o=(e+1)%t.length(),h=t.v[n],l=t.v[o],p=(s=h,a=[(i=l)[0]-s[0],i[1]-s[1]],r=.5*-Math.PI,[Math.cos(r)*a[0]-Math.sin(r)*a[1],Math.sin(r)*a[0]+Math.cos(r)*a[1]]);return Math.atan2(0,1)-Math.atan2(p[1],p[0])}function ce(t,e,s,i,a,r,n){var o=fe(e,s),h=e.v[s%e._length],l=e.v[0===s?e._length-1:s-1],p=e.v[(s+1)%e._length],f=2===r?Math.sqrt(Math.pow(h[0]-l[0],2)+Math.pow(h[1]-l[1],2)):0,c=2===r?Math.sqrt(Math.pow(h[0]-p[0],2)+Math.pow(h[1]-p[1],2)):0;pe(t,e.v[s%e._length],o,n,i,c/(2*(a+1)),f/(2*(a+1)))}function de(t,e,s,i,a,r){for(var n=0;n<i;n+=1){var o=(n+1)/(i+1),h=2===a?Math.sqrt(Math.pow(e.points[3][0]-e.points[0][0],2)+Math.pow(e.points[3][1]-e.points[0][1],2)):0,l=e.normalAngle(o);pe(t,e.point(o),l,r,s,h/(2*(i+1)),h/(2*(i+1))),r=-r}return r}function me(t,e,s){var i=Math.atan2(e[0]-t[0],e[1]-t[1]);return[ne(t,i,s),ne(e,i,s)]}function ue(t,e){var s,i,a,r,n,o,h;s=(h=me(t.points[0],t.points[1],e))[0],i=h[1],a=(h=me(t.points[1],t.points[2],e))[0],r=h[1],n=(h=me(t.points[2],t.points[3],e))[0],o=h[1];var l=re(s,i,a,r);null===l&&(l=i);var p=re(n,o,a,r);return null===p&&(p=n),new $t(s,l,p,o)}function ge(t,e,s,i,a){var r=e.points[3],n=s.points[0];if(3===i)return r;if(he(r,n))return r;if(2===i){var o=-e.tangentAngle(1),h=-s.tangentAngle(0)+Math.PI,l=re(r,ne(r,o+Math.PI/2,100),n,ne(n,o+Math.PI/2,100)),p=l?oe(l,r):oe(r,n)/2,f=ne(r,o,2*p*k);return t.setXYAt(f[0],f[1],"o",t.length()-1),f=ne(n,h,2*p*k),t.setTripleAt(n[0],n[1],n[0],n[1],f[0],f[1],t.length()),n}var c=re(he(r,e.points[2])?e.points[0]:e.points[2],r,n,he(n,s.points[1])?s.points[3]:s.points[1]);return c&&oe(c,r)<a?(t.setTripleAt(c[0],c[1],c[0],c[1],c[0],c[1],t.length()),c):r}function ye(t,e){var s=t.intersections(e);return s.length&&Gt(s[0][0],1)&&s.shift(),s.length?s[0]:null}function ve(t,e){var s=t.slice(),i=e.slice(),a=ye(t[t.length-1],e[0]);return a&&(s[t.length-1]=t[t.length-1].split(a[0])[0],i[0]=e[0].split(a[1])[1]),t.length>1&&e.length>1&&(a=ye(t[0],e[e.length-1]))?[[t[0].split(a[0])[0]],[e[e.length-1].split(a[1])[1]]]:[s,i]}function be(t,e){var s,i,a,r,n=t.inflectionPoints();if(0===n.length)return[ue(t,e)];if(1===n.length||Gt(n[1],1))return s=(a=t.split(n[0]))[0],i=a[1],[ue(s,e),ue(i,e)];s=(a=t.split(n[0]))[0];var o=(n[1]-n[0])/(1-n[0]);return r=(a=a[1].split(o))[0],i=a[1],[ue(s,e),ue(r,e),ue(i,e)]}function _e(){}function Ce(t){for(var e=t.fStyle?t.fStyle.split(" "):[],s="normal",i="normal",a=e.length,r=0;r<a;r+=1)switch(e[r].toLowerCase()){case"italic":i="italic";break;case"bold":s="700";break;case"black":s="900";break;case"medium":s="500";break;case"regular":case"normal":s="400";break;case"light":case"thin":s="200"}return{style:i,weight:t.fWeight||s}}r([Bt],Jt),Jt.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=vt.getProp(t,e.c,0,null,this),this.o=vt.getProp(t,e.o,0,null,this),this.tr=Yt.getTransformProperty(t,e.tr,this),this.so=vt.getProp(t,e.tr.so,0,.01,this),this.eo=vt.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Ft,this.rMatrix=new Ft,this.sMatrix=new Ft,this.tMatrix=new Ft,this.matrix=new Ft},Jt.prototype.applyTransforms=function(t,e,s,i,a,r){var n=r?-1:1,o=i.s.v[0]+(1-i.s.v[0])*(1-a),h=i.s.v[1]+(1-i.s.v[1])*(1-a);t.translate(i.p.v[0]*n*a,i.p.v[1]*n*a,i.p.v[2]),e.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),e.rotate(-i.r.v*n*a),e.translate(i.a.v[0],i.a.v[1],i.a.v[2]),s.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),s.scale(r?1/o:o,r?1/h:h),s.translate(i.a.v[0],i.a.v[1],i.a.v[2])},Jt.prototype.init=function(t,e,s,i){for(this.elem=t,this.arr=e,this.pos=s,this.elemsData=i,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[s]);s>0;)s-=1,this._elements.unshift(e[s]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},Jt.prototype.resetElements=function(t){var e,s=t.length;for(e=0;e<s;e+=1)t[e]._processed=!1,"gr"===t[e].ty&&this.resetElements(t[e].it)},Jt.prototype.cloneElements=function(t){var e=JSON.parse(JSON.stringify(t));return this.resetElements(e),e},Jt.prototype.changeGroupRender=function(t,e){var s,i=t.length;for(s=0;s<i;s+=1)t[s]._render=e,"gr"===t[s].ty&&this.changeGroupRender(t[s].it,e)},Jt.prototype.processShapes=function(t){var e,s,i,a,r,n=!1;if(this._mdf||t){var o,h=Math.ceil(this.c.v);if(this._groups.length<h){for(;this._groups.length<h;){var l={it:this.cloneElements(this._elements),ty:"gr"};l.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,l),this._groups.splice(0,0,l),this._currentCopies+=1}this.elem.reloadShapes(),n=!0}for(r=0,i=0;i<=this._groups.length-1;i+=1){if(o=r<h,this._groups[i]._render=o,this.changeGroupRender(this._groups[i].it,o),!o){var p=this.elemsData[i].it,f=p[p.length-1];0!==f.transform.op.v?(f.transform.op._mdf=!0,f.transform.op.v=0):f.transform.op._mdf=!1}r+=1}this._currentCopies=h;var c=this.o.v,d=c%1,m=c>0?Math.floor(c):Math.ceil(c),u=this.pMatrix.props,g=this.rMatrix.props,y=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var v,b,_=0;if(c>0){for(;_<m;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),_+=1;d&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,d,!1),_+=d)}else if(c<0){for(;_>m;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),_-=1;d&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-d,!0),_-=d)}for(i=1===this.data.m?0:this._currentCopies-1,a=1===this.data.m?1:-1,r=this._currentCopies;r;){if(b=(s=(e=this.elemsData[i].it)[e.length-1].transform.mProps.v.props).length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(i/(this._currentCopies-1)),0!==_){for((0!==i&&1===a||i!==this._currentCopies-1&&-1===a)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(g[0],g[1],g[2],g[3],g[4],g[5],g[6],g[7],g[8],g[9],g[10],g[11],g[12],g[13],g[14],g[15]),this.matrix.transform(y[0],y[1],y[2],y[3],y[4],y[5],y[6],y[7],y[8],y[9],y[10],y[11],y[12],y[13],y[14],y[15]),this.matrix.transform(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7],u[8],u[9],u[10],u[11],u[12],u[13],u[14],u[15]),v=0;v<b;v+=1)s[v]=this.matrix.props[v];this.matrix.reset()}else for(this.matrix.reset(),v=0;v<b;v+=1)s[v]=this.matrix.props[v];_+=1,r-=1,i+=a}}else for(r=this._currentCopies,i=0,a=1;r;)s=(e=this.elemsData[i].it)[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,r-=1,i+=a;return n},Jt.prototype.addShape=function(){},r([Bt],Ht),Ht.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=vt.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},Ht.prototype.processPath=function(t,e){var s,i=kt.newElement();i.c=t.c;var a,r,n,o,h,l,p,f,c,d,m,u,g=t._length,y=0;for(s=0;s<g;s+=1)a=t.v[s],n=t.o[s],r=t.i[s],a[0]===n[0]&&a[1]===n[1]&&a[0]===r[0]&&a[1]===r[1]?0!==s&&s!==g-1||t.c?(o=0===s?t.v[g-1]:t.v[s-1],l=(h=Math.sqrt(Math.pow(a[0]-o[0],2)+Math.pow(a[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=m=a[0]+(o[0]-a[0])*l,f=u=a[1]-(a[1]-o[1])*l,c=p-(p-a[0])*k,d=f-(f-a[1])*k,i.setTripleAt(p,f,c,d,m,u,y),y+=1,o=s===g-1?t.v[0]:t.v[s+1],l=(h=Math.sqrt(Math.pow(a[0]-o[0],2)+Math.pow(a[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=c=a[0]+(o[0]-a[0])*l,f=d=a[1]+(o[1]-a[1])*l,m=p-(p-a[0])*k,u=f-(f-a[1])*k,i.setTripleAt(p,f,c,d,m,u,y),y+=1):(i.setTripleAt(a[0],a[1],n[0],n[1],r[0],r[1],y),y+=1):(i.setTripleAt(t.v[s][0],t.v[s][1],t.o[s][0],t.o[s][1],t.i[s][0],t.i[s][1],y),y+=1);return i},Ht.prototype.processShapes=function(t){var e,s,i,a,r,n,o=this.shapes.length,h=this.rd.v;if(0!==h)for(s=0;s<o;s+=1){if(n=(r=this.shapes[s]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,i=0;i<a;i+=1)n.addShape(this.processPath(e[i],h));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},$t.prototype.point=function(t){return[((this.a[0]*t+this.b[0])*t+this.c[0])*t+this.d[0],((this.a[1]*t+this.b[1])*t+this.c[1])*t+this.d[1]]},$t.prototype.derivative=function(t){return[(3*t*this.a[0]+2*this.b[0])*t+this.c[0],(3*t*this.a[1]+2*this.b[1])*t+this.c[1]]},$t.prototype.tangentAngle=function(t){var e=this.derivative(t);return Math.atan2(e[1],e[0])},$t.prototype.normalAngle=function(t){var e=this.derivative(t);return Math.atan2(e[0],e[1])},$t.prototype.inflectionPoints=function(){var t=this.a[1]*this.b[0]-this.a[0]*this.b[1];if(Xt(t))return[];var e=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/t,s=e*e-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/t;if(s<0)return[];var i=Math.sqrt(s);return Xt(i)?i>0&&i<1?[e]:[]:[e-i,e+i].filter((function(t){return t>0&&t<1}))},$t.prototype.split=function(t){if(t<=0)return[Qt(this.points[0]),this];if(t>=1)return[this,Qt(this.points[this.points.length-1])];var e=Ut(this.points[0],this.points[1],t),s=Ut(this.points[1],this.points[2],t),i=Ut(this.points[2],this.points[3],t),a=Ut(e,s,t),r=Ut(s,i,t),n=Ut(a,r,t);return[new $t(this.points[0],e,a,n,!0),new $t(n,r,i,this.points[3],!0)]},$t.prototype.bounds=function(){return{x:te(this,0),y:te(this,1)}},$t.prototype.boundingBox=function(){var t=this.bounds();return{left:t.x.min,right:t.x.max,top:t.y.min,bottom:t.y.max,width:t.x.max-t.x.min,height:t.y.max-t.y.min,cx:(t.x.max+t.x.min)/2,cy:(t.y.max+t.y.min)/2}},$t.prototype.intersections=function(t,e,s){void 0===e&&(e=2),void 0===s&&(s=7);var i=[];return ie(ee(this,0,1),ee(t,0,1),0,e,i,s),i},$t.shapeSegment=function(t,e){var s=(e+1)%t.length();return new $t(t.v[e],t.o[e],t.i[s],t.v[s],!0)},$t.shapeSegmentInverted=function(t,e){var s=(e+1)%t.length();return new $t(t.v[s],t.i[s],t.o[e],t.v[e],!0)},r([Bt],le),le.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amplitude=vt.getProp(t,e.s,0,null,this),this.frequency=vt.getProp(t,e.r,0,null,this),this.pointsType=vt.getProp(t,e.pt,0,null,this),this._isAnimated=0!==this.amplitude.effectsSequence.length||0!==this.frequency.effectsSequence.length||0!==this.pointsType.effectsSequence.length},le.prototype.processPath=function(t,e,s,i){var a=t._length,r=kt.newElement();if(r.c=t.c,t.c||(a-=1),0===a)return r;var n=-1,o=$t.shapeSegment(t,0);ce(r,t,0,e,s,i,n);for(var h=0;h<a;h+=1)n=de(r,o,e,s,i,-n),o=h!==a-1||t.c?$t.shapeSegment(t,(h+1)%a):null,ce(r,t,h+1,e,s,i,n);return r},le.prototype.processShapes=function(t){var e,s,i,a,r,n,o=this.shapes.length,h=this.amplitude.v,l=Math.max(0,Math.round(this.frequency.v)),p=this.pointsType.v;if(0!==h)for(s=0;s<o;s+=1){if(n=(r=this.shapes[s]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,i=0;i<a;i+=1)n.addShape(this.processPath(e[i],h,l,p));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},r([Bt],_e),_e.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=vt.getProp(t,e.a,0,null,this),this.miterLimit=vt.getProp(t,e.ml,0,null,this),this.lineJoin=e.lj,this._isAnimated=0!==this.amount.effectsSequence.length},_e.prototype.processPath=function(t,e,s,i){var a=kt.newElement();a.c=t.c;var r,n,o,h=t.length();t.c||(h-=1);var l=[];for(r=0;r<h;r+=1)o=$t.shapeSegment(t,r),l.push(be(o,e));if(!t.c)for(r=h-1;r>=0;r-=1)o=$t.shapeSegmentInverted(t,r),l.push(be(o,e));l=function(t){for(var e,s=1;s<t.length;s+=1)e=ve(t[s-1],t[s]),t[s-1]=e[0],t[s]=e[1];return t.length>1&&(e=ve(t[t.length-1],t[0]),t[t.length-1]=e[0],t[0]=e[1]),t}(l);var p=null,f=null;for(r=0;r<l.length;r+=1){var c=l[r];for(f&&(p=ge(a,f,c[0],s,i)),f=c[c.length-1],n=0;n<c.length;n+=1)o=c[n],p&&he(o.points[0],p)?a.setXYAt(o.points[1][0],o.points[1][1],"o",a.length()-1):a.setTripleAt(o.points[0][0],o.points[0][1],o.points[1][0],o.points[1][1],o.points[0][0],o.points[0][1],a.length()),a.setTripleAt(o.points[3][0],o.points[3][1],o.points[3][0],o.points[3][1],o.points[2][0],o.points[2][1],a.length()),p=o.points[3]}return l.length&&ge(a,f,l[0][0],s,i),a},_e.prototype.processShapes=function(t){var e,s,i,a,r,n,o=this.shapes.length,h=this.amount.v,l=this.miterLimit.v,p=this.lineJoin;if(0!==h)for(s=0;s<o;s+=1){if(n=(r=this.shapes[s]).localShapeCollection,r.shape._mdf||this._mdf||t)for(n.releaseShapes(),r.shape._mdf=!0,e=r.shape.paths.shapes,a=r.shape.paths._length,i=0;i<a;i+=1)n.addShape(this.processPath(e[i],h,p,l));r.shape.paths=r.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var xe=function(){var t={w:0,size:0,shapes:[],data:{shapes:[]}},e=[];e=e.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var s=127988,i=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"];function r(t,e){var s=a("span");s.setAttribute("aria-hidden",!0),s.style.fontFamily=e;var i=a("span");i.innerText="giItT1WQy@!-/#",s.style.position="absolute",s.style.left="-10000px",s.style.top="-10000px",s.style.fontSize="300px",s.style.fontVariant="normal",s.style.fontStyle="normal",s.style.fontWeight="normal",s.style.letterSpacing="0",s.appendChild(i),document.body.appendChild(s);var r=i.offsetWidth;return i.style.fontFamily=function(t){var e,s=t.split(","),i=s.length,a=[];for(e=0;e<i;e+=1)"sans-serif"!==s[e]&&"monospace"!==s[e]&&a.push(s[e]);return a.join(",")}(t)+", "+e,{node:i,w:r,parent:s}}function n(t,e){var s,i=document.body&&e?"svg":"canvas",a=Ce(t);if("svg"===i){var r=j("text");r.style.fontSize="100px",r.setAttribute("font-family",t.fFamily),r.setAttribute("font-style",a.style),r.setAttribute("font-weight",a.weight),r.textContent="1",t.fClass?(r.style.fontFamily="inherit",r.setAttribute("class",t.fClass)):r.style.fontFamily=t.fFamily,e.appendChild(r),s=r}else{var n=new OffscreenCanvas(500,500).getContext("2d");n.font=a.style+" "+a.weight+" 100px "+t.fFamily,s=n}return{measureText:function(t){return"svg"===i?(s.textContent=t,s.getComputedTextLength()):s.measureText(t).width}}}function o(t){var e=0,s=t.charCodeAt(0);if(s>=55296&&s<=56319){var i=t.charCodeAt(1);i>=56320&&i<=57343&&(e=1024*(s-55296)+i-56320+65536)}return e}function h(t){var e=o(t);return e>=127462&&e<=127487}var l=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};l.isModifier=function(t,e){var s=t.toString(16)+e.toString(16);return-1!==i.indexOf(s)},l.isZeroWidthJoiner=function(t){return 8205===t},l.isFlagEmoji=function(t){return h(t.substr(0,2))&&h(t.substr(2,2))},l.isRegionalCode=h,l.isCombinedCharacter=function(t){return-1!==e.indexOf(t)},l.isRegionalFlag=function(t,e){var i=o(t.substr(e,2));if(i!==s)return!1;var a=0;for(e+=2;a<5;){if((i=o(t.substr(e,2)))<917601||i>917626)return!1;a+=1,e+=2}return 917631===o(t.substr(e,2))},l.isVariationSelector=function(t){return 65039===t},l.BLACK_FLAG_CODE_POINT=s;var p={addChars:function(t){if(t){var e;this.chars||(this.chars=[]);var s,i,a=t.length,r=this.chars.length;for(e=0;e<a;e+=1){for(s=0,i=!1;s<r;)this.chars[s].style===t[e].style&&this.chars[s].fFamily===t[e].fFamily&&this.chars[s].ch===t[e].ch&&(i=!0),s+=1;i||(this.chars.push(t[e]),r+=1)}}},addFonts:function(t,e){if(t){if(this.chars)return this.isLoaded=!0,void(this.fonts=t.list);if(!document.body)return this.isLoaded=!0,t.list.forEach((function(t){t.helper=n(t),t.cache={}})),void(this.fonts=t.list);var s,i=t.list,o=i.length,h=o;for(s=0;s<o;s+=1){var l,p,f=!0;if(i[s].loaded=!1,i[s].monoCase=r(i[s].fFamily,"monospace"),i[s].sansCase=r(i[s].fFamily,"sans-serif"),i[s].fPath){if("p"===i[s].fOrigin||3===i[s].origin){if((l=document.querySelectorAll('style[f-forigin="p"][f-family="'+i[s].fFamily+'"], style[f-origin="3"][f-family="'+i[s].fFamily+'"]')).length>0&&(f=!1),f){var c=a("style");c.setAttribute("f-forigin",i[s].fOrigin),c.setAttribute("f-origin",i[s].origin),c.setAttribute("f-family",i[s].fFamily),c.type="text/css",c.innerText="@font-face {font-family: "+i[s].fFamily+"; font-style: normal; src: url('"+i[s].fPath+"');}",e.appendChild(c)}}else if("g"===i[s].fOrigin||1===i[s].origin){for(l=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),p=0;p<l.length;p+=1)-1!==l[p].href.indexOf(i[s].fPath)&&(f=!1);if(f){var d=a("link");d.setAttribute("f-forigin",i[s].fOrigin),d.setAttribute("f-origin",i[s].origin),d.type="text/css",d.rel="stylesheet",d.href=i[s].fPath,document.body.appendChild(d)}}else if("t"===i[s].fOrigin||2===i[s].origin){for(l=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),p=0;p<l.length;p+=1)i[s].fPath===l[p].src&&(f=!1);if(f){var m=a("link");m.setAttribute("f-forigin",i[s].fOrigin),m.setAttribute("f-origin",i[s].origin),m.setAttribute("rel","stylesheet"),m.setAttribute("href",i[s].fPath),e.appendChild(m)}}}else i[s].loaded=!0,h-=1;i[s].helper=n(i[s],e),i[s].cache={},this.fonts.push(i[s])}0===h?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}else this.isLoaded=!0},getCharData:function(e,s,i){for(var a=0,r=this.chars.length;a<r;){if(this.chars[a].ch===e&&this.chars[a].style===s&&this.chars[a].fFamily===i)return this.chars[a];a+=1}return("string"==typeof e&&13!==e.charCodeAt(0)||!e)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",e,s,i)),t},getFontByName:function(t){for(var e=0,s=this.fonts.length;e<s;){if(this.fonts[e].fName===t)return this.fonts[e];e+=1}return this.fonts[0]},measureText:function(t,e,s){var i=this.getFontByName(e),a=t;if(!i.cache[a]){var r=i.helper;if(" "===t){var n=r.measureText("|"+t+"|"),o=r.measureText("||");i.cache[a]=(n-o)/100}else i.cache[a]=r.measureText(t)/100}return i.cache[a]*s},checkLoadedFonts:function(){var t,e,s,i=this.fonts.length,a=i;for(t=0;t<i;t+=1)this.fonts[t].loaded?a-=1:"n"===this.fonts[t].fOrigin||0===this.fonts[t].origin?this.fonts[t].loaded=!0:(e=this.fonts[t].monoCase.node,s=this.fonts[t].monoCase.w,e.offsetWidth!==s?(a-=1,this.fonts[t].loaded=!0):(e=this.fonts[t].sansCase.node,s=this.fonts[t].sansCase.w,e.offsetWidth!==s&&(a-=1,this.fonts[t].loaded=!0)),this.fonts[t].loaded&&(this.fonts[t].sansCase.parent.parentNode.removeChild(this.fonts[t].sansCase.parent),this.fonts[t].monoCase.parent.parentNode.removeChild(this.fonts[t].monoCase.parent)));0!==a&&Date.now()-this.initTime<5e3?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)},setIsLoaded:function(){this.isLoaded=!0}};return l.prototype=p,l}();function ke(t){this.animationData=t}function Se(){}ke.prototype.getProp=function(t){return this.animationData.slots&&this.animationData.slots[t.sid]?Object.assign(t,this.animationData.slots[t.sid].p):t},Se.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){-1===this.renderableComponents.indexOf(t)&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){-1!==this.renderableComponents.indexOf(t)&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var t,e=this.renderableComponents.length;for(t=0;t<e;t+=1)this.renderableComponents[t].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var Pe,De=(Pe={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"},function(t){return Pe[t]||""});function we(t,e,s){this.p=vt.getProp(e,t.v,0,0,s)}function Ae(t,e,s){this.p=vt.getProp(e,t.v,0,0,s)}function Me(t,e,s){this.p=vt.getProp(e,t.v,1,0,s)}function Te(t,e,s){this.p=vt.getProp(e,t.v,1,0,s)}function Fe(t,e,s){this.p=vt.getProp(e,t.v,0,0,s)}function Ee(t,e,s){this.p=vt.getProp(e,t.v,0,0,s)}function Le(t,e,s){this.p=vt.getProp(e,t.v,0,0,s)}function Ie(){this.p={}}function Re(t,e){var s,i=t.ef||[];this.effectElements=[];var a,r=i.length;for(s=0;s<r;s+=1)a=new Ve(i[s],e),this.effectElements.push(a)}function Ve(t,e){this.init(t,e)}function ze(){}function Oe(){}function Ne(t,e,s){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,s)}function qe(t,e,s){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,s),this._isPlaying=!1,this._canPlay=!1;var i=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(i),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?vt.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=vt.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}function Be(){}r([bt],Ve),Ve.prototype.getValue=Ve.prototype.iterateDynamicProperties,Ve.prototype.init=function(t,e){var s;this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);var i,a=this.data.ef.length,r=this.data.ef;for(s=0;s<a;s+=1){switch(i=null,r[s].ty){case 0:i=new we(r[s],e,this);break;case 1:i=new Ae(r[s],e,this);break;case 2:i=new Me(r[s],e,this);break;case 3:i=new Te(r[s],e,this);break;case 4:case 7:i=new Le(r[s],e,this);break;case 10:i=new Fe(r[s],e,this);break;case 11:i=new Ee(r[s],e,this);break;case 5:i=new Re(r[s],e,this);break;default:i=new Ie(r[s],e,this)}i&&this.effectElements.push(i)}},ze.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var t=0,e=this.data.masksProperties.length;t<e;){if("n"!==this.data.masksProperties[t].mode&&!1!==this.data.masksProperties[t].cl)return!0;t+=1}return!1},initExpressions:function(){var t=q();if(t){var e=t("layer"),s=t("effects"),i=t("shape"),a=t("text"),r=t("comp");this.layerInterface=e(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var n=s.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(n),0===this.data.ty||this.data.xt?this.compInterface=r(this):4===this.data.ty?(this.layerInterface.shapeInterface=i(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=a(this),this.layerInterface.text=this.layerInterface.textInterface)}},setBlendMode:function(){var t=De(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=t},initBaseData:function(t,e,s){this.globalData=e,this.comp=s,this.data=t,this.layerId=L(),this.data.sr||(this.data.sr=1),this.effectsManager=new Re(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},Oe.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,e){var s,i=this.dynamicProperties.length;for(s=0;s<i;s+=1)(e||this._isParent&&"transform"===this.dynamicProperties[s].propType)&&(this.dynamicProperties[s].getValue(),this.dynamicProperties[s]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&this.dynamicProperties.push(t)}},Ne.prototype.prepareFrame=function(){},r([Se,ze,Oe],Ne),Ne.prototype.getBaseElement=function(){return null},Ne.prototype.renderFrame=function(){},Ne.prototype.destroy=function(){},Ne.prototype.initExpressions=function(){var t=q();if(t){var e=t("footage");this.layerInterface=e(this)}},Ne.prototype.getFootageData=function(){return this.footageData},qe.prototype.prepareFrame=function(t){if(this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder)this._currentTime=t/this.data.sr;else{var e=this.tm.v;this._currentTime=e}this._volume=this.lv.v[0];var s=this._volume*this._volumeMultiplier;this._previousVolume!==s&&(this._previousVolume=s,this.audio.volume(s))},r([Se,ze,Oe],qe),qe.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},qe.prototype.show=function(){},qe.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},qe.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},qe.prototype.resume=function(){this._canPlay=!0},qe.prototype.setRate=function(t){this.audio.rate(t)},qe.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},qe.prototype.getBaseElement=function(){return null},qe.prototype.destroy=function(){},qe.prototype.sourceRectAtTime=function(){},qe.prototype.initExpressions=function(){},Be.prototype.checkLayers=function(t){var e,s,i=this.layers.length;for(this.completeLayers=!0,e=i-1;e>=0;e-=1)this.elements[e]||(s=this.layers[e]).ip-s.st<=t-this.layers[e].st&&s.op-s.st>t-this.layers[e].st&&this.buildItem(e),this.completeLayers=!!this.elements[e]&&this.completeLayers;this.checkPendingElements()},Be.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:default:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t)}},Be.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},Be.prototype.createAudio=function(t){return new qe(t,this.globalData,this)},Be.prototype.createFootage=function(t){return new Ne(t,this.globalData,this)},Be.prototype.buildAllItems=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.buildItem(t);this.checkPendingElements()},Be.prototype.includeLayers=function(t){var e;this.completeLayers=!1;var s,i=t.length,a=this.layers.length;for(e=0;e<i;e+=1)for(s=0;s<a;){if(this.layers[s].id===t[e].id){this.layers[s]=t[e];break}s+=1}},Be.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},Be.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},Be.prototype.buildElementParenting=function(t,e,s){for(var i=this.elements,a=this.layers,r=0,n=a.length;r<n;)a[r].ind==e&&(i[r]&&!0!==i[r]?(s.push(i[r]),i[r].setAsParent(),void 0!==a[r].parent?this.buildElementParenting(t,a[r].parent,s):t.setHierarchy(s)):(this.buildItem(r),this.addPendingElement(t))),r+=1},Be.prototype.addPendingElement=function(t){this.pendingElements.push(t)},Be.prototype.searchExtraCompositions=function(t){var e,s=t.length;for(e=0;e<s;e+=1)if(t[e].xt){var i=this.createComp(t[e]);i.initExpressions(),this.globalData.projectInterface.registerComposition(i)}},Be.prototype.getElementById=function(t){var e,s=this.elements.length;for(e=0;e<s;e+=1)if(this.elements[e].data.ind===t)return this.elements[e];return null},Be.prototype.getElementByPath=function(t){var e,s=t.shift();if("number"==typeof s)e=this.elements[s];else{var i,a=this.elements.length;for(i=0;i<a;i+=1)if(this.elements[i].data.nm===s){e=this.elements[i];break}}return 0===t.length?e:e.getElementByPath(t)},Be.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new xe,this.globalData.slotManager=function(t){return new ke(t)}(t),this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}};var We="transformEFfect";function je(){}function Ye(t,e,s){this.data=t,this.element=e,this.globalData=s,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var a,r,n=this.globalData.defs,o=this.masksProperties?this.masksProperties.length:0;this.viewData=l(o),this.solidPath="";var h,p,f,c,d,m,u=this.masksProperties,g=0,y=[],v=L(),b="clipPath",_="clip-path";for(a=0;a<o;a+=1)if(("a"!==u[a].mode&&"n"!==u[a].mode||u[a].inv||100!==u[a].o.k||u[a].o.x)&&(b="mask",_="mask"),"s"!==u[a].mode&&"i"!==u[a].mode||0!==g?f=null:((f=j("rect")).setAttribute("fill","#ffffff"),f.setAttribute("width",this.element.comp.data.w||0),f.setAttribute("height",this.element.comp.data.h||0),y.push(f)),r=j("path"),"n"===u[a].mode)this.viewData[a]={op:vt.getProp(this.element,u[a].o,0,.01,this.element),prop:Tt.getShapeProp(this.element,u[a],3),elem:r,lastPath:""},n.appendChild(r);else{var C;if(g+=1,r.setAttribute("fill","s"===u[a].mode?"#000000":"#ffffff"),r.setAttribute("clip-rule","nonzero"),0!==u[a].x.k?(b="mask",_="mask",m=vt.getProp(this.element,u[a].x,0,null,this.element),C=L(),(c=j("filter")).setAttribute("id",C),(d=j("feMorphology")).setAttribute("operator","erode"),d.setAttribute("in","SourceGraphic"),d.setAttribute("radius","0"),c.appendChild(d),n.appendChild(c),r.setAttribute("stroke","s"===u[a].mode?"#000000":"#ffffff")):(d=null,m=null),this.storedData[a]={elem:r,x:m,expan:d,lastPath:"",lastOperator:"",filterId:C,lastRadius:0},"i"===u[a].mode){p=y.length;var x=j("g");for(h=0;h<p;h+=1)x.appendChild(y[h]);var k=j("mask");k.setAttribute("mask-type","alpha"),k.setAttribute("id",v+"_"+g),k.appendChild(r),n.appendChild(k),x.setAttribute("mask","url("+i()+"#"+v+"_"+g+")"),y.length=0,y.push(x)}else y.push(r);u[a].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[a]={elem:r,lastPath:"",op:vt.getProp(this.element,u[a].o,0,.01,this.element),prop:Tt.getShapeProp(this.element,u[a],3),invRect:f},this.viewData[a].prop.k||this.drawPath(u[a],this.viewData[a].prop.v,this.viewData[a])}for(this.maskElement=j(b),o=y.length,a=0;a<o;a+=1)this.maskElement.appendChild(y[a]);g>0&&(this.maskElement.setAttribute("id",v),this.element.maskedElement.setAttribute(_,"url("+i()+"#"+v+")"),n.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}je.prototype={initTransform:function(){var t=new Ft;this.finalTransform={mProp:this.data.ks?Yt.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_localMatMdf:!1,_opMdf:!1,mat:t,localMat:t,localOpacity:1},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,e=this.finalTransform.mat,s=0,i=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;s<i;){if(this.hierarchy[s].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}s+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,e.cloneFromProps(t),s=0;s<i;s+=1)e.multiply(this.hierarchy[s].finalTransform.mProp.v)}this.localTransforms&&!this.finalTransform._matMdf||(this.finalTransform._localMatMdf=this.finalTransform._matMdf),this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v)},renderLocalTransform:function(){if(this.localTransforms){var t=0,e=this.localTransforms.length;if(this.finalTransform._localMatMdf=this.finalTransform._matMdf,!this.finalTransform._localMatMdf||!this.finalTransform._opMdf)for(;t<e;)this.localTransforms[t]._mdf&&(this.finalTransform._localMatMdf=!0),this.localTransforms[t]._opMdf&&!this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v,this.finalTransform._opMdf=!0),t+=1;if(this.finalTransform._localMatMdf){var s=this.finalTransform.localMat;for(this.localTransforms[0].matrix.clone(s),t=1;t<e;t+=1){var i=this.localTransforms[t].matrix;s.multiply(i)}s.multiply(this.finalTransform.mat)}if(this.finalTransform._opMdf){var a=this.finalTransform.localOpacity;for(t=0;t<e;t+=1)a*=.01*this.localTransforms[t].opacity;this.finalTransform.localOpacity=a}}},searchEffectTransforms:function(){if(this.renderableEffectsManager){var t=this.renderableEffectsManager.getEffects(We);if(t.length){this.localTransforms=[],this.finalTransform.localMat=new Ft;var e=0,s=t.length;for(e=0;e<s;e+=1)this.localTransforms.push(t[e])}}},globalToLocal:function(t){var e=[];e.push(this.finalTransform);for(var s,i=!0,a=this.comp;i;)a.finalTransform?(a.data.hasMask&&e.splice(0,0,a.finalTransform),a=a.comp):i=!1;var r,n=e.length;for(s=0;s<n;s+=1)r=e[s].mat.applyToPointArray(0,0,0),t=[t[0]-r[0],t[1]-r[1],0];return t},mHelper:new Ft},Ye.prototype.getMaskProperty=function(t){return this.viewData[t].prop},Ye.prototype.renderFrame=function(t){var e,s=this.element.finalTransform.mat,a=this.masksProperties.length;for(e=0;e<a;e+=1)if((this.viewData[e].prop._mdf||t)&&this.drawPath(this.masksProperties[e],this.viewData[e].prop.v,this.viewData[e]),(this.viewData[e].op._mdf||t)&&this.viewData[e].elem.setAttribute("fill-opacity",this.viewData[e].op.v),"n"!==this.masksProperties[e].mode&&(this.viewData[e].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[e].invRect.setAttribute("transform",s.getInverseMatrix().to2dCSS()),this.storedData[e].x&&(this.storedData[e].x._mdf||t))){var r=this.storedData[e].expan;this.storedData[e].x.v<0?("erode"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="erode",this.storedData[e].elem.setAttribute("filter","url("+i()+"#"+this.storedData[e].filterId+")")),r.setAttribute("radius",-this.storedData[e].x.v)):("dilate"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="dilate",this.storedData[e].elem.setAttribute("filter",null)),this.storedData[e].elem.setAttribute("stroke-width",2*this.storedData[e].x.v))}},Ye.prototype.getMaskelement=function(){return this.maskElement},Ye.prototype.createLayerSolidPath=function(){var t="M0,0 ";return t+=" h"+this.globalData.compSize.w,t+=" v"+this.globalData.compSize.h,t+=" h-"+this.globalData.compSize.w,t+=" v-"+this.globalData.compSize.h+" "},Ye.prototype.drawPath=function(t,e,s){var i,a,r=" M"+e.v[0][0]+","+e.v[0][1];for(a=e._length,i=1;i<a;i+=1)r+=" C"+e.o[i-1][0]+","+e.o[i-1][1]+" "+e.i[i][0]+","+e.i[i][1]+" "+e.v[i][0]+","+e.v[i][1];if(e.c&&a>1&&(r+=" C"+e.o[i-1][0]+","+e.o[i-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),s.lastPath!==r){var n="";s.elem&&(e.c&&(n=t.inv?this.solidPath+r:r),s.elem.setAttribute("d",n)),s.lastPath=r}},Ye.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var Je=function(){var t={};return t.createFilter=function(t,e){var s=j("filter");s.setAttribute("id",t),!0!==e&&(s.setAttribute("filterUnits","objectBoundingBox"),s.setAttribute("x","0%"),s.setAttribute("y","0%"),s.setAttribute("width","100%"),s.setAttribute("height","100%"));return s},t.createAlphaToLuminanceFilter=function(){var t=j("feColorMatrix");return t.setAttribute("type","matrix"),t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),t},t}(),He=function(){var t={maskType:!0,svgLumaHidden:!0,offscreenCanvas:"undefined"!=typeof OffscreenCanvas};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),/firefox/i.test(navigator.userAgent)&&(t.svgLumaHidden=!1),t}(),Ge={},Xe="filter_result_";function Ke(t){var e,s,a="SourceGraphic",r=t.data.ef?t.data.ef.length:0,n=L(),o=Je.createFilter(n,!0),h=0;for(this.filters=[],e=0;e<r;e+=1){s=null;var l=t.data.ef[e].ty;if(Ge[l])s=new(0,Ge[l].effect)(o,t.effectsManager.effectElements[e],t,Xe+h,a),a=Xe+h,Ge[l].countsAsEffect&&(h+=1);s&&this.filters.push(s)}h&&(t.globalData.defs.appendChild(o),t.layerElement.setAttribute("filter","url("+i()+"#"+n+")")),this.filters.length&&t.addRenderableComponent(this)}function Ue(){}function Ze(){}function Qe(){}function $e(t,e,s){this.assetData=e.getAssetData(t.refId),this.assetData&&this.assetData.sid&&(this.assetData=e.slotManager.getProp(this.assetData)),this.initElement(t,e,s),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function ts(t,e){this.elem=t,this.pos=e}function es(){}Ke.prototype.renderFrame=function(t){var e,s=this.filters.length;for(e=0;e<s;e+=1)this.filters[e].renderFrame(t)},Ke.prototype.getEffects=function(t){var e,s=this.filters.length,i=[];for(e=0;e<s;e+=1)this.filters[e].type===t&&i.push(this.filters[e]);return i},Ue.prototype={initRendererElement:function(){this.layerElement=j("g")},createContainerElements:function(){this.matteElement=j("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t=null;if(this.data.td){this.matteMasks={};var e=j("g");e.setAttribute("id",this.layerId),e.appendChild(this.layerElement),t=e,this.globalData.defs.appendChild(e)}else this.data.tt?(this.matteElement.appendChild(this.layerElement),t=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0===this.data.ty&&!this.data.hd){var s=j("clipPath"),a=j("path");a.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var r=L();if(s.setAttribute("id",r),s.appendChild(a),this.globalData.defs.appendChild(s),this.checkMasks()){var n=j("g");n.setAttribute("clip-path","url("+i()+"#"+r+")"),n.appendChild(this.layerElement),this.transformedElement=n,t?t.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+i()+"#"+r+")")}0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._localMatMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.localMat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.localOpacity)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new Ye(this.data,this,this.globalData),this.renderableEffectsManager=new Ke(this),this.searchEffectTransforms()},getMatte:function(t){if(this.matteMasks||(this.matteMasks={}),!this.matteMasks[t]){var e,s,a,r,n=this.layerId+"_"+t;if(1===t||3===t){var o=j("mask");o.setAttribute("id",n),o.setAttribute("mask-type",3===t?"luminance":"alpha"),(a=j("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),o.appendChild(a),this.globalData.defs.appendChild(o),He.maskType||1!==t||(o.setAttribute("mask-type","luminance"),e=L(),s=Je.createFilter(e),this.globalData.defs.appendChild(s),s.appendChild(Je.createAlphaToLuminanceFilter()),(r=j("g")).appendChild(a),o.appendChild(r),r.setAttribute("filter","url("+i()+"#"+e+")"))}else if(2===t){var h=j("mask");h.setAttribute("id",n),h.setAttribute("mask-type","alpha");var l=j("g");h.appendChild(l),e=L(),s=Je.createFilter(e);var p=j("feComponentTransfer");p.setAttribute("in","SourceGraphic"),s.appendChild(p);var f=j("feFuncA");f.setAttribute("type","table"),f.setAttribute("tableValues","1.0 0.0"),p.appendChild(f),this.globalData.defs.appendChild(s);var c=j("rect");c.setAttribute("width",this.comp.data.w),c.setAttribute("height",this.comp.data.h),c.setAttribute("x","0"),c.setAttribute("y","0"),c.setAttribute("fill","#ffffff"),c.setAttribute("opacity","0"),l.setAttribute("filter","url("+i()+"#"+e+")"),l.appendChild(c),(a=j("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),l.appendChild(a),He.maskType||(h.setAttribute("mask-type","luminance"),s.appendChild(Je.createAlphaToLuminanceFilter()),r=j("g"),l.appendChild(c),r.appendChild(this.layerElement),l.appendChild(r)),this.globalData.defs.appendChild(h)}this.matteMasks[t]=n}return this.matteMasks[t]},setMatte:function(t){this.matteElement&&this.matteElement.setAttribute("mask","url("+i()+"#"+t+")")}},Ze.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},r([Se,n({initElement:function(t,e,s){this.initFrame(),this.initBaseData(t,e,s),this.initTransform(t,e,s),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],Qe),r([ze,je,Ue,Ze,Oe,Qe],$e),$e.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=j("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},$e.prototype.sourceRectAtTime=function(){return this.sourceRect},es.prototype={addShapeToModifiers:function(t){var e,s=this.shapeModifiers.length;for(e=0;e<s;e+=1)this.shapeModifiers[e].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var e=this.shapeModifiers.length;0<e;)if(this.shapeModifiers[0].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var t,e=this.shapes.length;for(t=0;t<e;t+=1)this.shapes[t].sh.reset();for(t=(e=this.shapeModifiers.length)-1;t>=0&&!this.shapeModifiers[t].processShapes(this._isFirstFrame);t-=1);}},searchProcessedElement:function(t){for(var e=this.processedElements,s=0,i=e.length;s<i;){if(e[s].elem===t)return e[s].pos;s+=1}return 0},addProcessedElement:function(t,e){for(var s=this.processedElements,i=s.length;i;)if(s[i-=1].elem===t)return void(s[i].pos=e);s.push(new ts(t,e))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var ss={1:"butt",2:"round",3:"square"},is={1:"miter",2:"round",3:"bevel"};function as(t,e,s){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=s,this.lvl=e,this._isAnimated=!!s.k;for(var i=0,a=t.length;i<a;){if(t[i].mProps.dynamicProperties.length){this._isAnimated=!0;break}i+=1}}function rs(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=!0===t.hd,this.pElem=j("path"),this.msElem=null}function ns(t,e,s,i){var a;this.elem=t,this.frameId=-1,this.dataProps=l(e.length),this.renderer=s,this.k=!1,this.dashStr="",this.dashArray=h("float32",e.length?e.length-1:0),this.dashoffset=h("float32",1),this.initDynamicPropertyContainer(i);var r,n=e.length||0;for(a=0;a<n;a+=1)r=vt.getProp(t,e[a].v,0,0,this),this.k=r.k||this.k,this.dataProps[a]={n:e[a].n,p:r};this.k||this.getValue(!0),this._isAnimated=this.k}function os(t,e,s){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=vt.getProp(t,e.o,0,.01,this),this.w=vt.getProp(t,e.w,0,null,this),this.d=new ns(t,e.d||{},"svg",this),this.c=vt.getProp(t,e.c,1,255,this),this.style=s,this._isAnimated=!!this._isAnimated}function hs(t,e,s){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=vt.getProp(t,e.o,0,.01,this),this.c=vt.getProp(t,e.c,1,255,this),this.style=s}function ls(t,e,s){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=s}function ps(t,e,s){this.data=e,this.c=h("uint8c",4*e.p);var i=e.k.k[0].s?e.k.k[0].s.length-4*e.p:e.k.k.length-4*e.p;this.o=h("float32",i),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=i,this.initDynamicPropertyContainer(s),this.prop=vt.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function fs(t,e,s){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,s)}function cs(t,e,s){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=vt.getProp(t,e.w,0,null,this),this.d=new ns(t,e.d||{},"svg",this),this.initGradientData(t,e,s),this._isAnimated=!!this._isAnimated}function ds(){this.it=[],this.prevViewData=[],this.gr=j("g")}function ms(t,e,s){this.transform={mProps:t,op:e,container:s},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}as.prototype.setAsAnimated=function(){this._isAnimated=!0},rs.prototype.reset=function(){this.d="",this._mdf=!1},ns.prototype.getValue=function(t){if((this.elem.globalData.frameId!==this.frameId||t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,s=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),e=0;e<s;e+=1)"o"!==this.dataProps[e].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},r([bt],ns),r([bt],os),r([bt],hs),r([bt],ls),ps.prototype.comparePoints=function(t,e){for(var s=0,i=this.o.length/2;s<i;){if(Math.abs(t[4*s]-t[4*e+2*s])>.01)return!1;s+=1}return!0},ps.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},ps.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){var e,s,i,a=4*this.data.p;for(e=0;e<a;e+=1)s=e%4==0?100:255,i=Math.round(this.prop.v[e]*s),this.c[e]!==i&&(this.c[e]=i,this._cmdf=!t);if(this.o.length)for(a=this.prop.v.length,e=4*this.data.p;e<a;e+=1)s=e%2==0?100:1,i=e%2==0?Math.round(100*this.prop.v[e]):this.prop.v[e],this.o[e-4*this.data.p]!==i&&(this.o[e-4*this.data.p]=i,this._omdf=!t);this._mdf=!t}},r([bt],ps),fs.prototype.initGradientData=function(t,e,s){this.o=vt.getProp(t,e.o,0,.01,this),this.s=vt.getProp(t,e.s,1,null,this),this.e=vt.getProp(t,e.e,1,null,this),this.h=vt.getProp(t,e.h||{k:0},0,.01,this),this.a=vt.getProp(t,e.a||{k:0},0,x,this),this.g=new ps(t,e.g,this),this.style=s,this.stops=[],this.setGradientData(s.pElem,e),this.setGradientOpacity(e,s),this._isAnimated=!!this._isAnimated},fs.prototype.setGradientData=function(t,e){var s=L(),a=j(1===e.t?"linearGradient":"radialGradient");a.setAttribute("id",s),a.setAttribute("spreadMethod","pad"),a.setAttribute("gradientUnits","userSpaceOnUse");var r,n,o,h=[];for(o=4*e.g.p,n=0;n<o;n+=4)r=j("stop"),a.appendChild(r),h.push(r);t.setAttribute("gf"===e.ty?"fill":"stroke","url("+i()+"#"+s+")"),this.gf=a,this.cst=h},fs.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){var s,a,r,n=j("mask"),o=j("path");n.appendChild(o);var h=L(),l=L();n.setAttribute("id",l);var p=j(1===t.t?"linearGradient":"radialGradient");p.setAttribute("id",h),p.setAttribute("spreadMethod","pad"),p.setAttribute("gradientUnits","userSpaceOnUse"),r=t.g.k.k[0].s?t.g.k.k[0].s.length:t.g.k.k.length;var f=this.stops;for(a=4*t.g.p;a<r;a+=2)(s=j("stop")).setAttribute("stop-color","rgb(255,255,255)"),p.appendChild(s),f.push(s);o.setAttribute("gf"===t.ty?"fill":"stroke","url("+i()+"#"+h+")"),"gs"===t.ty&&(o.setAttribute("stroke-linecap",ss[t.lc||2]),o.setAttribute("stroke-linejoin",is[t.lj||2]),1===t.lj&&o.setAttribute("stroke-miterlimit",t.ml)),this.of=p,this.ms=n,this.ost=f,this.maskId=l,e.msElem=o}},r([bt],fs),r([fs,bt],cs);var us=function(t,e,s,i){if(0===e)return"";var a,r=t.o,n=t.i,o=t.v,h=" M"+i.applyToPointStringified(o[0][0],o[0][1]);for(a=1;a<e;a+=1)h+=" C"+i.applyToPointStringified(r[a-1][0],r[a-1][1])+" "+i.applyToPointStringified(n[a][0],n[a][1])+" "+i.applyToPointStringified(o[a][0],o[a][1]);return s&&e&&(h+=" C"+i.applyToPointStringified(r[a-1][0],r[a-1][1])+" "+i.applyToPointStringified(n[0][0],n[0][1])+" "+i.applyToPointStringified(o[0][0],o[0][1]),h+="z"),h},gs=function(){var t=new Ft,e=new Ft;function s(t,e,s){(s||e.transform.op._mdf)&&e.transform.container.setAttribute("opacity",e.transform.op.v),(s||e.transform.mProps._mdf)&&e.transform.container.setAttribute("transform",e.transform.mProps.v.to2dCSS())}function i(){}function a(s,i,a){var r,n,o,h,l,p,f,c,d,m,u=i.styles.length,g=i.lvl;for(p=0;p<u;p+=1){if(h=i.sh._mdf||a,i.styles[p].lvl<g){for(c=e.reset(),d=g-i.styles[p].lvl,m=i.transformers.length-1;!h&&d>0;)h=i.transformers[m].mProps._mdf||h,d-=1,m-=1;if(h)for(d=g-i.styles[p].lvl,m=i.transformers.length-1;d>0;)c.multiply(i.transformers[m].mProps.v),d-=1,m-=1}else c=t;if(n=(f=i.sh.paths)._length,h){for(o="",r=0;r<n;r+=1)(l=f.shapes[r])&&l._length&&(o+=us(l,l._length,l.c,c));i.caches[p]=o}else o=i.caches[p];i.styles[p].d+=!0===s.hd?"":o,i.styles[p]._mdf=h||i.styles[p]._mdf}}function r(t,e,s){var i=e.style;(e.c._mdf||s)&&i.pElem.setAttribute("fill","rgb("+v(e.c.v[0])+","+v(e.c.v[1])+","+v(e.c.v[2])+")"),(e.o._mdf||s)&&i.pElem.setAttribute("fill-opacity",e.o.v)}function n(t,e,s){o(t,e,s),h(t,e,s)}function o(t,e,s){var i,a,r,n,o,h=e.gf,l=e.g._hasOpacity,p=e.s.v,f=e.e.v;if(e.o._mdf||s){var c="gf"===t.ty?"fill-opacity":"stroke-opacity";e.style.pElem.setAttribute(c,e.o.v)}if(e.s._mdf||s){var d=1===t.t?"x1":"cx",m="x1"===d?"y1":"cy";h.setAttribute(d,p[0]),h.setAttribute(m,p[1]),l&&!e.g._collapsable&&(e.of.setAttribute(d,p[0]),e.of.setAttribute(m,p[1]))}if(e.g._cmdf||s){i=e.cst;var u=e.g.c;for(r=i.length,a=0;a<r;a+=1)(n=i[a]).setAttribute("offset",u[4*a]+"%"),n.setAttribute("stop-color","rgb("+u[4*a+1]+","+u[4*a+2]+","+u[4*a+3]+")")}if(l&&(e.g._omdf||s)){var g=e.g.o;for(r=(i=e.g._collapsable?e.cst:e.ost).length,a=0;a<r;a+=1)n=i[a],e.g._collapsable||n.setAttribute("offset",g[2*a]+"%"),n.setAttribute("stop-opacity",g[2*a+1])}if(1===t.t)(e.e._mdf||s)&&(h.setAttribute("x2",f[0]),h.setAttribute("y2",f[1]),l&&!e.g._collapsable&&(e.of.setAttribute("x2",f[0]),e.of.setAttribute("y2",f[1])));else if((e.s._mdf||e.e._mdf||s)&&(o=Math.sqrt(Math.pow(p[0]-f[0],2)+Math.pow(p[1]-f[1],2)),h.setAttribute("r",o),l&&!e.g._collapsable&&e.of.setAttribute("r",o)),e.s._mdf||e.e._mdf||e.h._mdf||e.a._mdf||s){o||(o=Math.sqrt(Math.pow(p[0]-f[0],2)+Math.pow(p[1]-f[1],2)));var y=Math.atan2(f[1]-p[1],f[0]-p[0]),v=e.h.v;v>=1?v=.99:v<=-1&&(v=-.99);var b=o*v,_=Math.cos(y+e.a.v)*b+p[0],C=Math.sin(y+e.a.v)*b+p[1];h.setAttribute("fx",_),h.setAttribute("fy",C),l&&!e.g._collapsable&&(e.of.setAttribute("fx",_),e.of.setAttribute("fy",C))}}function h(t,e,s){var i=e.style,a=e.d;a&&(a._mdf||s)&&a.dashStr&&(i.pElem.setAttribute("stroke-dasharray",a.dashStr),i.pElem.setAttribute("stroke-dashoffset",a.dashoffset[0])),e.c&&(e.c._mdf||s)&&i.pElem.setAttribute("stroke","rgb("+v(e.c.v[0])+","+v(e.c.v[1])+","+v(e.c.v[2])+")"),(e.o._mdf||s)&&i.pElem.setAttribute("stroke-opacity",e.o.v),(e.w._mdf||s)&&(i.pElem.setAttribute("stroke-width",e.w.v),i.msElem&&i.msElem.setAttribute("stroke-width",e.w.v))}return{createRenderFunction:function(t){switch(t.ty){case"fl":return r;case"gf":return o;case"gs":return n;case"st":return h;case"sh":case"el":case"rc":case"sr":return a;case"tr":return s;case"no":return i;default:return null}}}}();function ys(t,e,s){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,s),this.prevViewData=[]}function vs(t,e,s,i,a,r){this.o=t,this.sw=e,this.sc=s,this.fc=i,this.m=a,this.p=r,this._mdf={o:!0,sw:!!e,sc:!!s,fc:!!i,m:!0,p:!0}}function bs(t,e){this._frameId=s,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,e.d&&e.d.sid&&(e.d=t.globalData.slotManager.getProp(e.d)),this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}r([ze,je,Ue,es,Ze,Oe,Qe],ys),ys.prototype.initSecondaryElement=function(){},ys.prototype.identityMatrix=new Ft,ys.prototype.buildExpressionInterface=function(){},ys.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},ys.prototype.filterUniqueShapes=function(){var t,e,s,i,a=this.shapes.length,r=this.stylesList.length,n=[],o=!1;for(s=0;s<r;s+=1){for(i=this.stylesList[s],o=!1,n.length=0,t=0;t<a;t+=1)-1!==(e=this.shapes[t]).styles.indexOf(i)&&(n.push(e),o=e._isAnimated||o);n.length>1&&o&&this.setShapesAsAnimated(n)}},ys.prototype.setShapesAsAnimated=function(t){var e,s=t.length;for(e=0;e<s;e+=1)t[e].setAsAnimated()},ys.prototype.createStyleElement=function(t,e){var s,a=new rs(t,e),r=a.pElem;if("st"===t.ty)s=new os(this,t,a);else if("fl"===t.ty)s=new hs(this,t,a);else if("gf"===t.ty||"gs"===t.ty){s=new("gf"===t.ty?fs:cs)(this,t,a),this.globalData.defs.appendChild(s.gf),s.maskId&&(this.globalData.defs.appendChild(s.ms),this.globalData.defs.appendChild(s.of),r.setAttribute("mask","url("+i()+"#"+s.maskId+")"))}else"no"===t.ty&&(s=new ls(this,t,a));return"st"!==t.ty&&"gs"!==t.ty||(r.setAttribute("stroke-linecap",ss[t.lc||2]),r.setAttribute("stroke-linejoin",is[t.lj||2]),r.setAttribute("fill-opacity","0"),1===t.lj&&r.setAttribute("stroke-miterlimit",t.ml)),2===t.r&&r.setAttribute("fill-rule","evenodd"),t.ln&&r.setAttribute("id",t.ln),t.cl&&r.setAttribute("class",t.cl),t.bm&&(r.style["mix-blend-mode"]=De(t.bm)),this.stylesList.push(a),this.addToAnimatedContents(t,s),s},ys.prototype.createGroupElement=function(t){var e=new ds;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=De(t.bm)),e},ys.prototype.createTransformElement=function(t,e){var s=Yt.getTransformProperty(this,t,this),i=new ms(s,s.o,e);return this.addToAnimatedContents(t,i),i},ys.prototype.createShapeElement=function(t,e,s){var i=4;"rc"===t.ty?i=5:"el"===t.ty?i=6:"sr"===t.ty&&(i=7);var a=new as(e,s,Tt.getShapeProp(this,t,i,this));return this.shapes.push(a),this.addShapeToModifiers(a),this.addToAnimatedContents(t,a),a},ys.prototype.addToAnimatedContents=function(t,e){for(var s=0,i=this.animatedContents.length;s<i;){if(this.animatedContents[s].element===e)return;s+=1}this.animatedContents.push({fn:gs.createRenderFunction(t),element:e,data:t})},ys.prototype.setElementStyles=function(t){var e,s=t.styles,i=this.stylesList.length;for(e=0;e<i;e+=1)-1!==s.indexOf(this.stylesList[e])||this.stylesList[e].closed||s.push(this.stylesList[e])},ys.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers()},ys.prototype.searchShapes=function(t,e,s,i,a,r,n){var o,h,l,p,f,c,d=[].concat(r),m=t.length-1,u=[],g=[];for(o=m;o>=0;o-=1){if((c=this.searchProcessedElement(t[o]))?e[o]=s[c-1]:t[o]._render=n,"fl"===t[o].ty||"st"===t[o].ty||"gf"===t[o].ty||"gs"===t[o].ty||"no"===t[o].ty)c?e[o].style.closed=t[o].hd:e[o]=this.createStyleElement(t[o],a),t[o]._render&&e[o].style.pElem.parentNode!==i&&i.appendChild(e[o].style.pElem),u.push(e[o].style);else if("gr"===t[o].ty){if(c)for(l=e[o].it.length,h=0;h<l;h+=1)e[o].prevViewData[h]=e[o].it[h];else e[o]=this.createGroupElement(t[o]);this.searchShapes(t[o].it,e[o].it,e[o].prevViewData,e[o].gr,a+1,d,n),t[o]._render&&e[o].gr.parentNode!==i&&i.appendChild(e[o].gr)}else"tr"===t[o].ty?(c||(e[o]=this.createTransformElement(t[o],i)),p=e[o].transform,d.push(p)):"sh"===t[o].ty||"rc"===t[o].ty||"el"===t[o].ty||"sr"===t[o].ty?(c||(e[o]=this.createShapeElement(t[o],d,a)),this.setElementStyles(e[o])):"tm"===t[o].ty||"rd"===t[o].ty||"ms"===t[o].ty||"pb"===t[o].ty||"zz"===t[o].ty||"op"===t[o].ty?(c?(f=e[o]).closed=!1:((f=qt.getModifier(t[o].ty)).init(this,t[o]),e[o]=f,this.shapeModifiers.push(f)),g.push(f)):"rp"===t[o].ty&&(c?(f=e[o]).closed=!0:(f=qt.getModifier(t[o].ty),e[o]=f,f.init(this,t,o,e),this.shapeModifiers.push(f),n=!1),g.push(f));this.addProcessedElement(t[o],o+1)}for(m=u.length,o=0;o<m;o+=1)u[o].closed=!0;for(m=g.length,o=0;o<m;o+=1)g[o].closed=!0},ys.prototype.renderInnerContent=function(){var t;this.renderModifiers();var e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].reset();for(this.renderShape(),t=0;t<e;t+=1)(this.stylesList[t]._mdf||this._isFirstFrame)&&(this.stylesList[t].msElem&&(this.stylesList[t].msElem.setAttribute("d",this.stylesList[t].d),this.stylesList[t].d="M0 0"+this.stylesList[t].d),this.stylesList[t].pElem.setAttribute("d",this.stylesList[t].d||"M0 0"))},ys.prototype.renderShape=function(){var t,e,s=this.animatedContents.length;for(t=0;t<s;t+=1)e=this.animatedContents[t],(this._isFirstFrame||e.element._isAnimated)&&!0!==e.data&&e.fn(e.data,e.element,this._isFirstFrame)},ys.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},vs.prototype.update=function(t,e,s,i,a,r){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var n=!1;return this.o!==t&&(this.o=t,this._mdf.o=!0,n=!0),this.sw!==e&&(this.sw=e,this._mdf.sw=!0,n=!0),this.sc!==s&&(this.sc=s,this._mdf.sc=!0,n=!0),this.fc!==i&&(this.fc=i,this._mdf.fc=!0,n=!0),this.m!==a&&(this.m=a,this._mdf.m=!0,n=!0),!r.length||this.p[0]===r[0]&&this.p[1]===r[1]&&this.p[4]===r[4]&&this.p[5]===r[5]&&this.p[12]===r[12]&&this.p[13]===r[13]||(this.p=r,this._mdf.p=!0,n=!0),n},bs.prototype.defaultBoxWidth=[0,0],bs.prototype.copyData=function(t,e){for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s]);return t},bs.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},bs.prototype.searchProperty=function(){return this.searchKeyframes()},bs.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},bs.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},bs.prototype.getValue=function(t){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||t){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,s=this.keysIndex;if(this.lock)this.setCurrentData(this.currentData);else{var i;this.lock=!0,this._mdf=!1;var a=this.effectsSequence.length,r=t||this.data.d.k[this.keysIndex].s;for(i=0;i<a;i+=1)r=s!==this.keysIndex?this.effectsSequence[i](r,r.t):this.effectsSequence[i](this.currentData,r.t);e!==r&&this.setCurrentData(r),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}}},bs.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,s=0,i=t.length;s<=i-1&&!(s===i-1||t[s+1].t>e);)s+=1;return this.keysIndex!==s&&(this.keysIndex=s),this.data.d.k[this.keysIndex].s},bs.prototype.buildFinalText=function(t){for(var e,s,i=[],a=0,r=t.length,n=!1,o=!1,h="";a<r;)n=o,o=!1,e=t.charCodeAt(a),h=t.charAt(a),xe.isCombinedCharacter(e)?n=!0:e>=55296&&e<=56319?xe.isRegionalFlag(t,a)?h=t.substr(a,14):(s=t.charCodeAt(a+1))>=56320&&s<=57343&&(xe.isModifier(e,s)?(h=t.substr(a,2),n=!0):h=xe.isFlagEmoji(t.substr(a,4))?t.substr(a,4):t.substr(a,2)):e>56319?(s=t.charCodeAt(a+1),xe.isVariationSelector(e)&&(n=!0)):xe.isZeroWidthJoiner(e)&&(n=!0,o=!0),n?(i[i.length-1]+=h,n=!1):i.push(h),a+=h.length;return i},bs.prototype.completeTextData=function(t){t.__complete=!0;var e,s,i,a,r,n,o,h=this.elem.globalData.fontManager,l=this.data,p=[],f=0,c=l.m.g,d=0,m=0,u=0,g=[],y=0,v=0,b=h.getFontByName(t.f),_=0,C=Ce(b);t.fWeight=C.weight,t.fStyle=C.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),s=t.finalText.length,t.finalLineHeight=t.lh;var x,k=t.tr/1e3*t.finalSize;if(t.sz)for(var S,P,D=!0,w=t.sz[0],A=t.sz[1];D;){S=0,y=0,s=(P=this.buildFinalText(t.t)).length,k=t.tr/1e3*t.finalSize;var M=-1;for(e=0;e<s;e+=1)x=P[e].charCodeAt(0),i=!1," "===P[e]?M=e:13!==x&&3!==x||(y=0,i=!0,S+=t.finalLineHeight||1.2*t.finalSize),h.chars?(o=h.getCharData(P[e],b.fStyle,b.fFamily),_=i?0:o.w*t.finalSize/100):_=h.measureText(P[e],t.f,t.finalSize),y+_>w&&" "!==P[e]?(-1===M?s+=1:e=M,S+=t.finalLineHeight||1.2*t.finalSize,P.splice(e,M===e?1:0,"\r"),M=-1,y=0):(y+=_,y+=k);S+=b.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&A<S?(t.finalSize-=1,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=P,s=t.finalText.length,D=!1)}y=-k,_=0;var T,F=0;for(e=0;e<s;e+=1)if(i=!1,13===(x=(T=t.finalText[e]).charCodeAt(0))||3===x?(F=0,g.push(y),v=y>v?y:v,y=-2*k,a="",i=!0,u+=1):a=T,h.chars?(o=h.getCharData(T,b.fStyle,h.getFontByName(t.f).fFamily),_=i?0:o.w*t.finalSize/100):_=h.measureText(a,t.f,t.finalSize)," "===T?F+=_+k:(y+=_+k+F,F=0),p.push({l:_,an:_,add:d,n:i,anIndexes:[],val:a,line:u,animatorJustifyOffset:0}),2==c){if(d+=_,""===a||" "===a||e===s-1){for(""!==a&&" "!==a||(d-=_);m<=e;)p[m].an=d,p[m].ind=f,p[m].extra=_,m+=1;f+=1,d=0}}else if(3==c){if(d+=_,""===a||e===s-1){for(""===a&&(d-=_);m<=e;)p[m].an=d,p[m].ind=f,p[m].extra=_,m+=1;d=0,f+=1}}else p[f].ind=f,p[f].extra=0,f+=1;if(t.l=p,v=y>v?y:v,g.push(y),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=v,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=g;var E,L,I,R,V=l.a;n=V.length;var z=[];for(r=0;r<n;r+=1){for((E=V[r]).a.sc&&(t.strokeColorAnim=!0),E.a.sw&&(t.strokeWidthAnim=!0),(E.a.fc||E.a.fh||E.a.fs||E.a.fb)&&(t.fillColorAnim=!0),R=0,I=E.s.b,e=0;e<s;e+=1)(L=p[e]).anIndexes[r]=R,(1==I&&""!==L.val||2==I&&""!==L.val&&" "!==L.val||3==I&&(L.n||" "==L.val||e==s-1)||4==I&&(L.n||e==s-1))&&(1===E.s.rn&&z.push(R),R+=1);l.a[r].s.totalChars=R;var O,N=-1;if(1===E.s.rn)for(e=0;e<s;e+=1)N!=(L=p[e]).anIndexes[r]&&(N=L.anIndexes[r],O=z.splice(Math.floor(Math.random()*z.length),1)[0]),L.anIndexes[r]=O}t.yOffset=t.finalLineHeight||1.2*t.finalSize,t.ls=t.ls||0,t.ascent=b.ascent*t.finalSize/100},bs.prototype.updateDocumentData=function(t,e){e=void 0===e?this.keysIndex:e;var s=this.copyData({},this.data.d.k[e].s);s=this.copyData(s,t),this.data.d.k[e].s=s,this.recalculate(e),this.setCurrentData(s),this.elem.addDynamicProperty(this)},bs.prototype.recalculate=function(t){var e=this.data.d.k[t].s;e.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(e)},bs.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},bs.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var _s=function(){var t=Math.max,e=Math.min,s=Math.floor;function i(t,e){this._currentTextLength=-1,this.k=!1,this.data=e,this.elem=t,this.comp=t.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(t),this.s=vt.getProp(t,e.s||{k:0},0,0,this),this.e="e"in e?vt.getProp(t,e.e,0,0,this):{v:100},this.o=vt.getProp(t,e.o||{k:0},0,0,this),this.xe=vt.getProp(t,e.xe||{k:0},0,0,this),this.ne=vt.getProp(t,e.ne||{k:0},0,0,this),this.sm=vt.getProp(t,e.sm||{k:100},0,0,this),this.a=vt.getProp(t,e.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return i.prototype={getMult:function(i){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var a=0,r=0,n=1,o=1;this.ne.v>0?a=this.ne.v/100:r=-this.ne.v/100,this.xe.v>0?n=1-this.xe.v/100:o=1+this.xe.v/100;var h=tt.getBezierEasing(a,r,n,o).get,l=0,p=this.finalS,f=this.finalE,c=this.data.sh;if(2===c)l=h(l=f===p?i>=f?1:0:t(0,e(.5/(f-p)+(i-p)/(f-p),1)));else if(3===c)l=h(l=f===p?i>=f?0:1:1-t(0,e(.5/(f-p)+(i-p)/(f-p),1)));else if(4===c)f===p?l=0:(l=t(0,e(.5/(f-p)+(i-p)/(f-p),1)))<.5?l*=2:l=1-2*(l-.5),l=h(l);else if(5===c){if(f===p)l=0;else{var d=f-p,m=-d/2+(i=e(t(0,i+.5-p),f-p)),u=d/2;l=Math.sqrt(1-m*m/(u*u))}l=h(l)}else 6===c?(f===p?l=0:(i=e(t(0,i+.5-p),f-p),l=(1+Math.cos(Math.PI+2*Math.PI*i/(f-p)))/2),l=h(l)):(i>=s(p)&&(l=t(0,e(i-p<0?e(f,1)-(p-i):f-i,1))),l=h(l));if(100!==this.sm.v){var g=.01*this.sm.v;0===g&&(g=1e-8);var y=.5-.5*g;l<y?l=0:(l=(l-y)/g)>1&&(l=1)}return l*this.a.v},getValue:function(t){this.iterateDynamicProperties(),this._mdf=t||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,t&&2===this.data.r&&(this.e.v=this._currentTextLength);var e=2===this.data.r?1:100/this.data.totalChars,s=this.o.v/e,i=this.s.v/e+s,a=this.e.v/e+s;if(i>a){var r=i;i=a,a=r}this.finalS=i,this.finalE=a}},r([bt],i),{getTextSelectorProp:function(t,e,s){return new i(t,e,s)}}}();function Cs(t,e,s){var i={propType:!1},a=vt.getProp,r=e.a;this.a={r:r.r?a(t,r.r,0,x,s):i,rx:r.rx?a(t,r.rx,0,x,s):i,ry:r.ry?a(t,r.ry,0,x,s):i,sk:r.sk?a(t,r.sk,0,x,s):i,sa:r.sa?a(t,r.sa,0,x,s):i,s:r.s?a(t,r.s,1,.01,s):i,a:r.a?a(t,r.a,1,0,s):i,o:r.o?a(t,r.o,0,.01,s):i,p:r.p?a(t,r.p,1,0,s):i,sw:r.sw?a(t,r.sw,0,0,s):i,sc:r.sc?a(t,r.sc,1,0,s):i,fc:r.fc?a(t,r.fc,1,0,s):i,fh:r.fh?a(t,r.fh,0,0,s):i,fs:r.fs?a(t,r.fs,0,.01,s):i,fb:r.fb?a(t,r.fb,0,.01,s):i,t:r.t?a(t,r.t,0,0,s):i},this.s=_s.getTextSelectorProp(t,e.s,s),this.s.t=e.s.t}function xs(t,e,s){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=s,this._animatorsData=l(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(s)}function ks(){}xs.prototype.searchProperties=function(){var t,e,s=this._textData.a.length,i=vt.getProp;for(t=0;t<s;t+=1)e=this._textData.a[t],this._animatorsData[t]=new Cs(this._elem,e,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:i(this._elem,this._textData.p.a,0,0,this),f:i(this._elem,this._textData.p.f,0,0,this),l:i(this._elem,this._textData.p.l,0,0,this),r:i(this._elem,this._textData.p.r,0,0,this),p:i(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=i(this._elem,this._textData.m.a,1,0,this)},xs.prototype.getMeasures=function(t,e){if(this.lettersChangedFlag=e,this._mdf||this._isFirstFrame||e||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var s,i,a,r,n,o,h,l,p,f,c,d,m,u,g,y,v,b,_,C=this._moreOptions.alignment.v,x=this._animatorsData,k=this._textData,S=this.mHelper,P=this._renderType,D=this.renderedLetters.length,w=t.l;if(this._hasMaskedPath){if(_=this._pathData.m,!this._pathData.n||this._pathData._mdf){var A,M=_.v;for(this._pathData.r.v&&(M=M.reverse()),n={tLength:0,segments:[]},r=M._length-1,y=0,a=0;a<r;a+=1)A=rt.buildBezierData(M.v[a],M.v[a+1],[M.o[a][0]-M.v[a][0],M.o[a][1]-M.v[a][1]],[M.i[a+1][0]-M.v[a+1][0],M.i[a+1][1]-M.v[a+1][1]]),n.tLength+=A.segmentLength,n.segments.push(A),y+=A.segmentLength;a=r,_.v.c&&(A=rt.buildBezierData(M.v[a],M.v[0],[M.o[a][0]-M.v[a][0],M.o[a][1]-M.v[a][1]],[M.i[0][0]-M.v[0][0],M.i[0][1]-M.v[0][1]]),n.tLength+=A.segmentLength,n.segments.push(A),y+=A.segmentLength),this._pathData.pi=n}if(n=this._pathData.pi,o=this._pathData.f.v,c=0,f=1,l=0,p=!0,u=n.segments,o<0&&_.v.c)for(n.tLength<Math.abs(o)&&(o=-Math.abs(o)%n.tLength),f=(m=u[c=u.length-1].points).length-1;o<0;)o+=m[f].partialLength,(f-=1)<0&&(f=(m=u[c-=1].points).length-1);d=(m=u[c].points)[f-1],g=(h=m[f]).partialLength}r=w.length,s=0,i=0;var T,F,E,L,I,R=1.2*t.finalSize*.714,N=!0;E=x.length;var q,B,W,j,Y,J,H,G,X,K,U,Z,Q=-1,$=o,tt=c,et=f,st=-1,it="",at=this.defaultPropsArray;if(2===t.j||1===t.j){var nt=0,ot=0,ht=2===t.j?-.5:-1,lt=0,pt=!0;for(a=0;a<r;a+=1)if(w[a].n){for(nt&&(nt+=ot);lt<a;)w[lt].animatorJustifyOffset=nt,lt+=1;nt=0,pt=!0}else{for(F=0;F<E;F+=1)(T=x[F].a).t.propType&&(pt&&2===t.j&&(ot+=T.t.v*ht),(I=x[F].s.getMult(w[a].anIndexes[F],k.a[F].s.totalChars)).length?nt+=T.t.v*I[0]*ht:nt+=T.t.v*I*ht);pt=!1}for(nt&&(nt+=ot);lt<a;)w[lt].animatorJustifyOffset=nt,lt+=1}for(a=0;a<r;a+=1){if(S.reset(),j=1,w[a].n)s=0,i+=t.yOffset,i+=N?1:0,o=$,N=!1,this._hasMaskedPath&&(f=et,d=(m=u[c=tt].points)[f-1],g=(h=m[f]).partialLength,l=0),it="",U="",X="",Z="",at=this.defaultPropsArray;else{if(this._hasMaskedPath){if(st!==w[a].line){switch(t.j){case 1:o+=y-t.lineWidths[w[a].line];break;case 2:o+=(y-t.lineWidths[w[a].line])/2}st=w[a].line}Q!==w[a].ind&&(w[Q]&&(o+=w[Q].extra),o+=w[a].an/2,Q=w[a].ind),o+=C[0]*w[a].an*.005;var ft=0;for(F=0;F<E;F+=1)(T=x[F].a).p.propType&&((I=x[F].s.getMult(w[a].anIndexes[F],k.a[F].s.totalChars)).length?ft+=T.p.v[0]*I[0]:ft+=T.p.v[0]*I),T.a.propType&&((I=x[F].s.getMult(w[a].anIndexes[F],k.a[F].s.totalChars)).length?ft+=T.a.v[0]*I[0]:ft+=T.a.v[0]*I);for(p=!0,this._pathData.a.v&&(o=.5*w[0].an+(y-this._pathData.f.v-.5*w[0].an-.5*w[w.length-1].an)*Q/(r-1),o+=this._pathData.f.v);p;)l+g>=o+ft||!m?(v=(o+ft-l)/h.partialLength,B=d.point[0]+(h.point[0]-d.point[0])*v,W=d.point[1]+(h.point[1]-d.point[1])*v,S.translate(-C[0]*w[a].an*.005,-C[1]*R*.01),p=!1):m&&(l+=h.partialLength,(f+=1)>=m.length&&(f=0,u[c+=1]?m=u[c].points:_.v.c?(f=0,m=u[c=0].points):(l-=h.partialLength,m=null)),m&&(d=h,g=(h=m[f]).partialLength));q=w[a].an/2-w[a].add,S.translate(-q,0,0)}else q=w[a].an/2-w[a].add,S.translate(-q,0,0),S.translate(-C[0]*w[a].an*.005,-C[1]*R*.01,0);for(F=0;F<E;F+=1)(T=x[F].a).t.propType&&(I=x[F].s.getMult(w[a].anIndexes[F],k.a[F].s.totalChars),0===s&&0===t.j||(this._hasMaskedPath?I.length?o+=T.t.v*I[0]:o+=T.t.v*I:I.length?s+=T.t.v*I[0]:s+=T.t.v*I));for(t.strokeWidthAnim&&(J=t.sw||0),t.strokeColorAnim&&(Y=t.sc?[t.sc[0],t.sc[1],t.sc[2]]:[0,0,0]),t.fillColorAnim&&t.fc&&(H=[t.fc[0],t.fc[1],t.fc[2]]),F=0;F<E;F+=1)(T=x[F].a).a.propType&&((I=x[F].s.getMult(w[a].anIndexes[F],k.a[F].s.totalChars)).length?S.translate(-T.a.v[0]*I[0],-T.a.v[1]*I[1],T.a.v[2]*I[2]):S.translate(-T.a.v[0]*I,-T.a.v[1]*I,T.a.v[2]*I));for(F=0;F<E;F+=1)(T=x[F].a).s.propType&&((I=x[F].s.getMult(w[a].anIndexes[F],k.a[F].s.totalChars)).length?S.scale(1+(T.s.v[0]-1)*I[0],1+(T.s.v[1]-1)*I[1],1):S.scale(1+(T.s.v[0]-1)*I,1+(T.s.v[1]-1)*I,1));for(F=0;F<E;F+=1){if(T=x[F].a,I=x[F].s.getMult(w[a].anIndexes[F],k.a[F].s.totalChars),T.sk.propType&&(I.length?S.skewFromAxis(-T.sk.v*I[0],T.sa.v*I[1]):S.skewFromAxis(-T.sk.v*I,T.sa.v*I)),T.r.propType&&(I.length?S.rotateZ(-T.r.v*I[2]):S.rotateZ(-T.r.v*I)),T.ry.propType&&(I.length?S.rotateY(T.ry.v*I[1]):S.rotateY(T.ry.v*I)),T.rx.propType&&(I.length?S.rotateX(T.rx.v*I[0]):S.rotateX(T.rx.v*I)),T.o.propType&&(I.length?j+=(T.o.v*I[0]-j)*I[0]:j+=(T.o.v*I-j)*I),t.strokeWidthAnim&&T.sw.propType&&(I.length?J+=T.sw.v*I[0]:J+=T.sw.v*I),t.strokeColorAnim&&T.sc.propType)for(G=0;G<3;G+=1)I.length?Y[G]+=(T.sc.v[G]-Y[G])*I[0]:Y[G]+=(T.sc.v[G]-Y[G])*I;if(t.fillColorAnim&&t.fc){if(T.fc.propType)for(G=0;G<3;G+=1)I.length?H[G]+=(T.fc.v[G]-H[G])*I[0]:H[G]+=(T.fc.v[G]-H[G])*I;T.fh.propType&&(H=I.length?O(H,T.fh.v*I[0]):O(H,T.fh.v*I)),T.fs.propType&&(H=I.length?V(H,T.fs.v*I[0]):V(H,T.fs.v*I)),T.fb.propType&&(H=I.length?z(H,T.fb.v*I[0]):z(H,T.fb.v*I))}}for(F=0;F<E;F+=1)(T=x[F].a).p.propType&&(I=x[F].s.getMult(w[a].anIndexes[F],k.a[F].s.totalChars),this._hasMaskedPath?I.length?S.translate(0,T.p.v[1]*I[0],-T.p.v[2]*I[1]):S.translate(0,T.p.v[1]*I,-T.p.v[2]*I):I.length?S.translate(T.p.v[0]*I[0],T.p.v[1]*I[1],-T.p.v[2]*I[2]):S.translate(T.p.v[0]*I,T.p.v[1]*I,-T.p.v[2]*I));if(t.strokeWidthAnim&&(X=J<0?0:J),t.strokeColorAnim&&(K="rgb("+Math.round(255*Y[0])+","+Math.round(255*Y[1])+","+Math.round(255*Y[2])+")"),t.fillColorAnim&&t.fc&&(U="rgb("+Math.round(255*H[0])+","+Math.round(255*H[1])+","+Math.round(255*H[2])+")"),this._hasMaskedPath){if(S.translate(0,-t.ls),S.translate(0,C[1]*R*.01+i,0),this._pathData.p.v){b=(h.point[1]-d.point[1])/(h.point[0]-d.point[0]);var ct=180*Math.atan(b)/Math.PI;h.point[0]<d.point[0]&&(ct+=180),S.rotate(-ct*Math.PI/180)}S.translate(B,W,0),o-=C[0]*w[a].an*.005,w[a+1]&&Q!==w[a+1].ind&&(o+=w[a].an/2,o+=.001*t.tr*t.finalSize)}else{switch(S.translate(s,i,0),t.ps&&S.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:S.translate(w[a].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[w[a].line]),0,0);break;case 2:S.translate(w[a].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[w[a].line])/2,0,0)}S.translate(0,-t.ls),S.translate(q,0,0),S.translate(C[0]*w[a].an*.005,C[1]*R*.01,0),s+=w[a].l+.001*t.tr*t.finalSize}"html"===P?it=S.toCSS():"svg"===P?it=S.to2dCSS():at=[S.props[0],S.props[1],S.props[2],S.props[3],S.props[4],S.props[5],S.props[6],S.props[7],S.props[8],S.props[9],S.props[10],S.props[11],S.props[12],S.props[13],S.props[14],S.props[15]],Z=j}D<=a?(L=new vs(Z,X,K,U,it,at),this.renderedLetters.push(L),D+=1,this.lettersChangedFlag=!0):(L=this.renderedLetters[a],this.lettersChangedFlag=L.update(Z,X,K,U,it,at)||this.lettersChangedFlag)}}},xs.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},xs.prototype.mHelper=new Ft,xs.prototype.defaultPropsArray=[],r([bt],xs),ks.prototype.initElement=function(t,e,s){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,s),this.textProperty=new bs(this,t.t,this.dynamicProperties),this.textAnimator=new xs(t.t,this.renderType,this),this.initTransform(t,e,s),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ks.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)},ks.prototype.createPathShape=function(t,e){var s,i,a=e.length,r="";for(s=0;s<a;s+=1)"sh"===e[s].ty&&(i=e[s].ks.k,r+=us(i,i.i.length,!0,t));return r},ks.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},ks.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},ks.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},ks.prototype.applyTextPropertiesToMatrix=function(t,e,s,i,a){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[s]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[s])/2,0,0)}e.translate(i,a,0)},ks.prototype.buildColor=function(t){return"rgb("+Math.round(255*t[0])+","+Math.round(255*t[1])+","+Math.round(255*t[2])+")"},ks.prototype.emptyProp=new vs,ks.prototype.destroy=function(){},ks.prototype.validateText=function(){(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)};var Ss={shapes:[]};function Ps(t,e,s){this.textSpans=[],this.renderType="svg",this.initElement(t,e,s)}function Ds(t,e,s){this.initElement(t,e,s)}function ws(t,e,s){this.initFrame(),this.initBaseData(t,e,s),this.initFrame(),this.initTransform(t,e,s),this.initHierarchy()}function As(){}function Ms(){}function Ts(t,e,s){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?l(this.layers.length):[],this.initElement(t,e,s),this.tm=t.tm?vt.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function Fs(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=j("svg");var s="";if(e&&e.title){var i=j("title"),a=L();i.setAttribute("id",a),i.textContent=e.title,this.svgElement.appendChild(i),s+=a}if(e&&e.description){var r=j("desc"),n=L();r.setAttribute("id",n),r.textContent=e.description,this.svgElement.appendChild(r),s+=" "+n}s&&this.svgElement.setAttribute("aria-labelledby",s);var o=j("defs");this.svgElement.appendChild(o);var h=j("g");this.svgElement.appendChild(h),this.layerElement=h,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&!1===e.hideOnTransparent),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height,runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:o,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}function Es(){this.sequences={},this.sequenceList=[],this.transform_key_count=0}r([ze,je,Ue,Ze,Oe,Qe,ks],Ps),Ps.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=j("text"))},Ps.prototype.buildTextContents=function(t){for(var e=0,s=t.length,i=[],a="";e<s;)t[e]===String.fromCharCode(13)||t[e]===String.fromCharCode(3)?(i.push(a),a=""):a+=t[e],e+=1;return i.push(a),i},Ps.prototype.buildShapeData=function(t,e){if(t.shapes&&t.shapes.length){var s=t.shapes[0];if(s.it){var i=s.it[s.it.length-1];i.s&&(i.s.k[0]=e,i.s.k[1]=e)}}return t},Ps.prototype.buildNewText=function(){var t,e;this.addDynamicProperty(this);var s=this.textProperty.currentData;this.renderedLetters=l(s?s.l.length:0),s.fc?this.layerElement.setAttribute("fill",this.buildColor(s.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),s.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(s.sc)),this.layerElement.setAttribute("stroke-width",s.sw)),this.layerElement.setAttribute("font-size",s.finalSize);var i=this.globalData.fontManager.getFontByName(s.f);if(i.fClass)this.layerElement.setAttribute("class",i.fClass);else{this.layerElement.setAttribute("font-family",i.fFamily);var a=s.fWeight,r=s.fStyle;this.layerElement.setAttribute("font-style",r),this.layerElement.setAttribute("font-weight",a)}this.layerElement.setAttribute("aria-label",s.t);var n,o=s.l||[],h=!!this.globalData.fontManager.chars;e=o.length;var p=this.mHelper,f=this.data.singleShape,c=0,d=0,m=!0,u=.001*s.tr*s.finalSize;if(!f||h||s.sz){var g,y=this.textSpans.length;for(t=0;t<e;t+=1){if(this.textSpans[t]||(this.textSpans[t]={span:null,childSpan:null,glyph:null}),!h||!f||0===t){if(n=y>t?this.textSpans[t].span:j(h?"g":"text"),y<=t){if(n.setAttribute("stroke-linecap","butt"),n.setAttribute("stroke-linejoin","round"),n.setAttribute("stroke-miterlimit","4"),this.textSpans[t].span=n,h){var v=j("g");n.appendChild(v),this.textSpans[t].childSpan=v}this.textSpans[t].span=n,this.layerElement.appendChild(n)}n.style.display="inherit"}if(p.reset(),f&&(o[t].n&&(c=-u,d+=s.yOffset,d+=m?1:0,m=!1),this.applyTextPropertiesToMatrix(s,p,o[t].line,c,d),c+=o[t].l||0,c+=u),h){var b;if(1===(g=this.globalData.fontManager.getCharData(s.finalText[t],i.fStyle,this.globalData.fontManager.getFontByName(s.f).fFamily)).t)b=new Ts(g.data,this.globalData,this);else{var _=Ss;g.data&&g.data.shapes&&(_=this.buildShapeData(g.data,s.finalSize)),b=new ys(_,this.globalData,this)}if(this.textSpans[t].glyph){var C=this.textSpans[t].glyph;this.textSpans[t].childSpan.removeChild(C.layerElement),C.destroy()}this.textSpans[t].glyph=b,b._debug=!0,b.prepareFrame(0),b.renderFrame(),this.textSpans[t].childSpan.appendChild(b.layerElement),1===g.t&&this.textSpans[t].childSpan.setAttribute("transform","scale("+s.finalSize/100+","+s.finalSize/100+")")}else f&&n.setAttribute("transform","translate("+p.props[12]+","+p.props[13]+")"),n.textContent=o[t].val,n.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}f&&n&&n.setAttribute("d","")}else{var x=this.textContainer,k="start";switch(s.j){case 1:k="end";break;case 2:k="middle";break;default:k="start"}x.setAttribute("text-anchor",k),x.setAttribute("letter-spacing",u);var S=this.buildTextContents(s.finalText);for(e=S.length,d=s.ps?s.ps[1]+s.ascent:0,t=0;t<e;t+=1)(n=this.textSpans[t].span||j("tspan")).textContent=S[t],n.setAttribute("x",0),n.setAttribute("y",d),n.style.display="inherit",x.appendChild(n),this.textSpans[t]||(this.textSpans[t]={span:null,glyph:null}),this.textSpans[t].span=n,d+=s.finalLineHeight;this.layerElement.appendChild(x)}for(;t<this.textSpans.length;)this.textSpans[t].span.style.display="none",t+=1;this._sizeChanged=!0},Ps.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var t=this.layerElement.getBBox();this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}}return this.bbox},Ps.prototype.getValue=function(){var t,e,s=this.textSpans.length;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<s;t+=1)(e=this.textSpans[t].glyph)&&(e.prepareFrame(this.comp.renderedFrame-this.data.st),e._mdf&&(this._mdf=!0))},Ps.prototype.renderInnerContent=function(){if(this.validateText(),(!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){var t,e;this._sizeChanged=!0;var s,i,a,r=this.textAnimator.renderedLetters,n=this.textProperty.currentData.l;for(e=n.length,t=0;t<e;t+=1)n[t].n||(s=r[t],i=this.textSpans[t].span,(a=this.textSpans[t].glyph)&&a.renderFrame(),s._mdf.m&&i.setAttribute("transform",s.m),s._mdf.o&&i.setAttribute("opacity",s.o),s._mdf.sw&&i.setAttribute("stroke-width",s.sw),s._mdf.sc&&i.setAttribute("stroke",s.sc),s._mdf.fc&&i.setAttribute("fill",s.fc))}},r([$e],Ds),Ds.prototype.createContent=function(){var t=j("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)},ws.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},ws.prototype.renderFrame=function(){},ws.prototype.getBaseElement=function(){return null},ws.prototype.destroy=function(){},ws.prototype.sourceRectAtTime=function(){},ws.prototype.hide=function(){},r([ze,je,Ze,Oe],ws),r([Be],As),As.prototype.createNull=function(t){return new ws(t,this.globalData,this)},As.prototype.createShape=function(t){return new ys(t,this.globalData,this)},As.prototype.createText=function(t){return new Ps(t,this.globalData,this)},As.prototype.createImage=function(t){return new $e(t,this.globalData,this)},As.prototype.createSolid=function(t){return new Ds(t,this.globalData,this)},As.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs;this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t;var s=j("clipPath"),a=j("rect");a.setAttribute("width",t.w),a.setAttribute("height",t.h),a.setAttribute("x",0),a.setAttribute("y",0);var r=L();s.setAttribute("id",r),s.appendChild(a),this.layerElement.setAttribute("clip-path","url("+i()+"#"+r+")"),e.appendChild(s),this.layers=t.layers,this.elements=l(t.layers.length)},As.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},As.prototype.updateContainerSize=function(){},As.prototype.findIndexByInd=function(t){var e=0,s=this.layers.length;for(e=0;e<s;e+=1)if(this.layers[e].ind===t)return e;return-1},As.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){e[t]=!0;var s=this.createItem(this.layers[t]);if(e[t]=s,N()&&(0===this.layers[t].ty&&this.globalData.projectInterface.registerComposition(s),s.initExpressions()),this.appendElementInPos(s,t),this.layers[t].tt){var i="tp"in this.layers[t]?this.findIndexByInd(this.layers[t].tp):t-1;if(-1===i)return;if(this.elements[i]&&!0!==this.elements[i]){var a=e[i].getMatte(this.layers[t].tt);s.setMatte(a)}else this.buildItem(i),this.addPendingElement(s)}}},As.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,s=this.elements.length;e<s;){if(this.elements[e]===t){var i="tp"in t.data?this.findIndexByInd(t.data.tp):e-1,a=this.elements[i].getMatte(this.layers[e].tt);t.setMatte(a);break}e+=1}}},As.prototype.renderFrame=function(t){if(this.renderedFrame!==t&&!this.destroyed){var e;null===t?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var s=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=s-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<s;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},As.prototype.appendElementInPos=function(t,e){var s=t.getBaseElement();if(s){for(var i,a=0;a<e;)this.elements[a]&&!0!==this.elements[a]&&this.elements[a].getBaseElement()&&(i=this.elements[a].getBaseElement()),a+=1;i?this.layerElement.insertBefore(s,i):this.layerElement.appendChild(s)}},As.prototype.hide=function(){this.layerElement.style.display="none"},As.prototype.show=function(){this.layerElement.style.display="block"},r([ze,je,Ze,Oe,Qe],Ms),Ms.prototype.initElement=function(t,e,s){this.initFrame(),this.initBaseData(t,e,s),this.initTransform(t,e,s),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),!this.data.xt&&e.progressiveLoad||this.buildAllItems(),this.hide()},Ms.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.isInRange||this.data.xt){if(this.tm._placeholder)this.renderedFrame=t/this.data.sr;else{var e=this.tm.v;e===this.data.op&&(e=this.data.op-1),this.renderedFrame=e}var s,i=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),s=i-1;s>=0;s-=1)(this.completeLayers||this.elements[s])&&(this.elements[s].prepareFrame(this.renderedFrame-this.layers[s].st),this.elements[s]._mdf&&(this._mdf=!0))}},Ms.prototype.renderInnerContent=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},Ms.prototype.setElements=function(t){this.elements=t},Ms.prototype.getElements=function(){return this.elements},Ms.prototype.destroyElements=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy()},Ms.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},r([As,Ms,Ue],Ts),Ts.prototype.createComp=function(t){return new Ts(t,this.globalData,this)},r([As],Fs),Fs.prototype.createComp=function(t){return new Ts(t,this.globalData,this)},Es.prototype={addTransformSequence:function(t){var e,s=t.length,i="_";for(e=0;e<s;e+=1)i+=t[e].transform.key+"_";var a=this.sequences[i];return a||(a={transforms:[].concat(t),finalTransform:new Ft,_mdf:!1},this.sequences[i]=a,this.sequenceList.push(a)),a},processSequence:function(t,e){for(var s=0,i=t.transforms.length,a=e;s<i&&!e;){if(t.transforms[s].transform.mProps._mdf){a=!0;break}s+=1}if(a)for(t.finalTransform.reset(),s=i-1;s>=0;s-=1)t.finalTransform.multiply(t.transforms[s].transform.mProps.v);t._mdf=a},processSequences:function(t){var e,s=this.sequenceList.length;for(e=0;e<s;e+=1)this.processSequence(this.sequenceList[e],t)},getNewKey:function(){return this.transform_key_count+=1,"_"+this.transform_key_count}};var Ls=function(){var t="__lottie_element_luma_buffer",e=null,s=null,i=null;function r(){var r,n,o;e||(r=j("svg"),n=j("filter"),o=j("feColorMatrix"),n.setAttribute("id",t),o.setAttribute("type","matrix"),o.setAttribute("color-interpolation-filters","sRGB"),o.setAttribute("values","0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0"),n.appendChild(o),r.appendChild(n),r.setAttribute("id",t+"_svg"),He.svgLumaHidden&&(r.style.display="none"),i=r,document.body.appendChild(i),e=a("canvas"),(s=e.getContext("2d")).filter="url(#"+t+")",s.fillStyle="rgba(0,0,0,0)",s.fillRect(0,0,1,1))}return{load:r,get:function(i){return e||r(),e.width=i.width,e.height=i.height,s.filter="url(#"+t+")",e}}};function Is(t,e){if(He.offscreenCanvas)return new OffscreenCanvas(t,e);var s=a("canvas");return s.width=t,s.height=e,s}var Rs={loadLumaCanvas:Ls.load,getLumaCanvas:Ls.get,createCanvas:Is},Vs={};function zs(t){var e,s,i=t.data.ef?t.data.ef.length:0;for(this.filters=[],e=0;e<i;e+=1){s=null;var a=t.data.ef[e].ty;if(Vs[a])s=new(0,Vs[a].effect)(t.effectsManager.effectElements[e],t);s&&this.filters.push(s)}this.filters.length&&t.addRenderableComponent(this)}function Os(t,e){var s;this.data=t,this.element=e,this.masksProperties=this.data.masksProperties||[],this.viewData=l(this.masksProperties.length);var i=this.masksProperties.length,a=!1;for(s=0;s<i;s+=1)"n"!==this.masksProperties[s].mode&&(a=!0),this.viewData[s]=Tt.getShapeProp(this.element,this.masksProperties[s],3);this.hasMasks=a,a&&this.element.addRenderableComponent(this)}function Ns(){}zs.prototype.renderFrame=function(t){var e,s=this.filters.length;for(e=0;e<s;e+=1)this.filters[e].renderFrame(t)},zs.prototype.getEffects=function(t){var e,s=this.filters.length,i=[];for(e=0;e<s;e+=1)this.filters[e].type===t&&i.push(this.filters[e]);return i},Os.prototype.renderFrame=function(){if(this.hasMasks){var t,e,s,i,a=this.element.finalTransform.mat,r=this.element.canvasContext,n=this.masksProperties.length;for(r.beginPath(),t=0;t<n;t+=1)if("n"!==this.masksProperties[t].mode){var o;this.masksProperties[t].inv&&(r.moveTo(0,0),r.lineTo(this.element.globalData.compSize.w,0),r.lineTo(this.element.globalData.compSize.w,this.element.globalData.compSize.h),r.lineTo(0,this.element.globalData.compSize.h),r.lineTo(0,0)),i=this.viewData[t].v,e=a.applyToPointArray(i.v[0][0],i.v[0][1],0),r.moveTo(e[0],e[1]);var h=i._length;for(o=1;o<h;o+=1)s=a.applyToTriplePoints(i.o[o-1],i.i[o],i.v[o]),r.bezierCurveTo(s[0],s[1],s[2],s[3],s[4],s[5]);s=a.applyToTriplePoints(i.o[o-1],i.i[0],i.v[0]),r.bezierCurveTo(s[0],s[1],s[2],s[3],s[4],s[5])}this.element.globalData.renderer.save(!0),r.clip()}},Os.prototype.getMaskProperty=Ye.prototype.getMaskProperty,Os.prototype.destroy=function(){this.element=null};var qs,Bs={1:"source-in",2:"source-out",3:"source-in",4:"source-out"};function Ws(t,e,s,i){this.styledShapes=[],this.tr=[0,0,0,0,0,0];var a,r=4;"rc"===e.ty?r=5:"el"===e.ty?r=6:"sr"===e.ty&&(r=7),this.sh=Tt.getShapeProp(t,e,r,t);var n,o=s.length;for(a=0;a<o;a+=1)s[a].closed||(n={transforms:i.addTransformSequence(s[a].transforms),trNodes:[]},this.styledShapes.push(n),s[a].elements.push(n))}function js(t,e,s){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.itemsData=[],this.prevViewData=[],this.shapeModifiers=[],this.processedElements=[],this.transformsManager=new Es,this.initElement(t,e,s)}function Ys(t,e,s){this.textSpans=[],this.yOffset=0,this.fillColorAnim=!1,this.strokeColorAnim=!1,this.strokeWidthAnim=!1,this.stroke=!1,this.fill=!1,this.justifyOffset=0,this.currentRender=null,this.renderType="canvas",this.values={fill:"rgba(0,0,0,0)",stroke:"rgba(0,0,0,0)",sWidth:0,fValue:""},this.initElement(t,e,s)}function Js(t,e,s){this.assetData=e.getAssetData(t.refId),this.img=e.imageLoader.getAsset(this.assetData),this.initElement(t,e,s)}function Hs(t,e,s){this.initElement(t,e,s)}function Gs(){}function Xs(){this.opacity=-1,this.transform=h("float32",16),this.fillStyle="",this.strokeStyle="",this.lineWidth="",this.lineCap="",this.lineJoin="",this.miterLimit="",this.id=Math.random()}function Ks(){var t;this.stack=[],this.cArrPos=0,this.cTr=new Ft;for(t=0;t<15;t+=1){var e=new Xs;this.stack[t]=e}this._length=15,this.nativeContext=null,this.transformMat=new Ft,this.currentOpacity=1,this.currentFillStyle="",this.appliedFillStyle="",this.currentStrokeStyle="",this.appliedStrokeStyle="",this.currentLineWidth="",this.appliedLineWidth="",this.currentLineCap="",this.appliedLineCap="",this.currentLineJoin="",this.appliedLineJoin="",this.appliedMiterLimit="",this.currentMiterLimit=""}function Us(t,e,s){this.completeLayers=!1,this.layers=t.layers,this.pendingElements=[],this.elements=l(this.layers.length),this.initElement(t,e,s),this.tm=t.tm?vt.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function Zs(t,e){this.animationItem=t,this.renderConfig={clearCanvas:!e||void 0===e.clearCanvas||e.clearCanvas,context:e&&e.context||null,progressiveLoad:e&&e.progressiveLoad||!1,preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",className:e&&e.className||"",id:e&&e.id||"",runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.renderConfig.dpr=e&&e.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=e&&e.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new Ks,this.elements=[],this.pendingElements=[],this.transformMat=new Ft,this.completeLayers=!1,this.rendererType="canvas",this.renderConfig.clearCanvas&&(this.ctxTransform=this.contextData.transform.bind(this.contextData),this.ctxOpacity=this.contextData.opacity.bind(this.contextData),this.ctxFillStyle=this.contextData.fillStyle.bind(this.contextData),this.ctxStrokeStyle=this.contextData.strokeStyle.bind(this.contextData),this.ctxLineWidth=this.contextData.lineWidth.bind(this.contextData),this.ctxLineCap=this.contextData.lineCap.bind(this.contextData),this.ctxLineJoin=this.contextData.lineJoin.bind(this.contextData),this.ctxMiterLimit=this.contextData.miterLimit.bind(this.contextData),this.ctxFill=this.contextData.fill.bind(this.contextData),this.ctxFillRect=this.contextData.fillRect.bind(this.contextData),this.ctxStroke=this.contextData.stroke.bind(this.contextData),this.save=this.contextData.save.bind(this.contextData))}return Ns.prototype={createElements:function(){},initRendererElement:function(){},createContainerElements:function(){if(this.data.tt>=1){this.buffers=[];var t=this.globalData.canvasContext,e=Rs.createCanvas(t.canvas.width,t.canvas.height);this.buffers.push(e);var s=Rs.createCanvas(t.canvas.width,t.canvas.height);this.buffers.push(s),this.data.tt>=3&&!document._isProxy&&Rs.loadLumaCanvas()}this.canvasContext=this.globalData.canvasContext,this.transformCanvas=this.globalData.transformCanvas,this.renderableEffectsManager=new zs(this),this.searchEffectTransforms()},createContent:function(){},setBlendMode:function(){var t=this.globalData;if(t.blendMode!==this.data.bm){t.blendMode=this.data.bm;var e=De(this.data.bm);t.canvasContext.globalCompositeOperation=e}},createRenderableComponents:function(){this.maskManager=new Os(this.data,this),this.transformEffects=this.renderableEffectsManager.getEffects(We)},hideElement:function(){this.hidden||this.isInRange&&!this.isTransparent||(this.hidden=!0)},showElement:function(){this.isInRange&&!this.isTransparent&&(this.hidden=!1,this._isFirstFrame=!0,this.maskManager._isFirstFrame=!0)},clearCanvas:function(t){t.clearRect(this.transformCanvas.tx,this.transformCanvas.ty,this.transformCanvas.w*this.transformCanvas.sx,this.transformCanvas.h*this.transformCanvas.sy)},prepareLayer:function(){if(this.data.tt>=1){var t=this.buffers[0].getContext("2d");this.clearCanvas(t),t.drawImage(this.canvasContext.canvas,0,0),this.currentTransform=this.canvasContext.getTransform(),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform)}},exitLayer:function(){if(this.data.tt>=1){var t=this.buffers[1],e=t.getContext("2d");if(this.clearCanvas(e),e.drawImage(this.canvasContext.canvas,0,0),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform),this.comp.getElementById("tp"in this.data?this.data.tp:this.data.ind-1).renderFrame(!0),this.canvasContext.setTransform(1,0,0,1,0,0),this.data.tt>=3&&!document._isProxy){var s=Rs.getLumaCanvas(this.canvasContext.canvas);s.getContext("2d").drawImage(this.canvasContext.canvas,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.drawImage(s,0,0)}this.canvasContext.globalCompositeOperation=Bs[this.data.tt],this.canvasContext.drawImage(t,0,0),this.canvasContext.globalCompositeOperation="destination-over",this.canvasContext.drawImage(this.buffers[0],0,0),this.canvasContext.setTransform(this.currentTransform),this.canvasContext.globalCompositeOperation="source-over"}},renderFrame:function(t){if(!this.hidden&&!this.data.hd&&(1!==this.data.td||t)){this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.setBlendMode();var e=0===this.data.ty;this.prepareLayer(),this.globalData.renderer.save(e),this.globalData.renderer.ctxTransform(this.finalTransform.localMat.props),this.globalData.renderer.ctxOpacity(this.finalTransform.localOpacity),this.renderInnerContent(),this.globalData.renderer.restore(e),this.exitLayer(),this.maskManager.hasMasks&&this.globalData.renderer.restore(!0),this._isFirstFrame&&(this._isFirstFrame=!1)}},destroy:function(){this.canvasContext=null,this.data=null,this.globalData=null,this.maskManager.destroy()},mHelper:new Ft},Ns.prototype.hide=Ns.prototype.hideElement,Ns.prototype.show=Ns.prototype.showElement,Ws.prototype.setAsAnimated=as.prototype.setAsAnimated,r([ze,je,Ns,es,Ze,Oe,Se],js),js.prototype.initElement=Qe.prototype.initElement,js.prototype.transformHelper={opacity:1,_opMdf:!1},js.prototype.dashResetter=[],js.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[])},js.prototype.createStyleElement=function(t,e){var s={data:t,type:t.ty,preTransforms:this.transformsManager.addTransformSequence(e),transforms:[],elements:[],closed:!0===t.hd},i={};if("fl"===t.ty||"st"===t.ty?(i.c=vt.getProp(this,t.c,1,255,this),i.c.k||(s.co="rgb("+v(i.c.v[0])+","+v(i.c.v[1])+","+v(i.c.v[2])+")")):"gf"!==t.ty&&"gs"!==t.ty||(i.s=vt.getProp(this,t.s,1,null,this),i.e=vt.getProp(this,t.e,1,null,this),i.h=vt.getProp(this,t.h||{k:0},0,.01,this),i.a=vt.getProp(this,t.a||{k:0},0,x,this),i.g=new ps(this,t.g,this)),i.o=vt.getProp(this,t.o,0,.01,this),"st"===t.ty||"gs"===t.ty){if(s.lc=ss[t.lc||2],s.lj=is[t.lj||2],1==t.lj&&(s.ml=t.ml),i.w=vt.getProp(this,t.w,0,null,this),i.w.k||(s.wi=i.w.v),t.d){var a=new ns(this,t.d,"canvas",this);i.d=a,i.d.k||(s.da=i.d.dashArray,s.do=i.d.dashoffset[0])}}else s.r=2===t.r?"evenodd":"nonzero";return this.stylesList.push(s),i.style=s,i},js.prototype.createGroupElement=function(){return{it:[],prevViewData:[]}},js.prototype.createTransformElement=function(t){return{transform:{opacity:1,_opMdf:!1,key:this.transformsManager.getNewKey(),op:vt.getProp(this,t.o,0,.01,this),mProps:Yt.getTransformProperty(this,t,this)}}},js.prototype.createShapeElement=function(t){var e=new Ws(this,t,this.stylesList,this.transformsManager);return this.shapes.push(e),this.addShapeToModifiers(e),e},js.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[]),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame)},js.prototype.addTransformToStyleList=function(t){var e,s=this.stylesList.length;for(e=0;e<s;e+=1)this.stylesList[e].closed||this.stylesList[e].transforms.push(t)},js.prototype.removeTransformFromStyleList=function(){var t,e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].closed||this.stylesList[t].transforms.pop()},js.prototype.closeStyles=function(t){var e,s=t.length;for(e=0;e<s;e+=1)t[e].closed=!0},js.prototype.searchShapes=function(t,e,s,i,a){var r,n,o,h,l,p,f=t.length-1,c=[],d=[],m=[].concat(a);for(r=f;r>=0;r-=1){if((h=this.searchProcessedElement(t[r]))?e[r]=s[h-1]:t[r]._shouldRender=i,"fl"===t[r].ty||"st"===t[r].ty||"gf"===t[r].ty||"gs"===t[r].ty)h?e[r].style.closed=!1:e[r]=this.createStyleElement(t[r],m),c.push(e[r].style);else if("gr"===t[r].ty){if(h)for(o=e[r].it.length,n=0;n<o;n+=1)e[r].prevViewData[n]=e[r].it[n];else e[r]=this.createGroupElement(t[r]);this.searchShapes(t[r].it,e[r].it,e[r].prevViewData,i,m)}else"tr"===t[r].ty?(h||(p=this.createTransformElement(t[r]),e[r]=p),m.push(e[r]),this.addTransformToStyleList(e[r])):"sh"===t[r].ty||"rc"===t[r].ty||"el"===t[r].ty||"sr"===t[r].ty?h||(e[r]=this.createShapeElement(t[r])):"tm"===t[r].ty||"rd"===t[r].ty||"pb"===t[r].ty||"zz"===t[r].ty||"op"===t[r].ty?(h?(l=e[r]).closed=!1:((l=qt.getModifier(t[r].ty)).init(this,t[r]),e[r]=l,this.shapeModifiers.push(l)),d.push(l)):"rp"===t[r].ty&&(h?(l=e[r]).closed=!0:(l=qt.getModifier(t[r].ty),e[r]=l,l.init(this,t,r,e),this.shapeModifiers.push(l),i=!1),d.push(l));this.addProcessedElement(t[r],r+1)}for(this.removeTransformFromStyleList(),this.closeStyles(c),f=d.length,r=0;r<f;r+=1)d[r].closed=!0},js.prototype.renderInnerContent=function(){this.transformHelper.opacity=1,this.transformHelper._opMdf=!1,this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame),this.renderShape(this.transformHelper,this.shapesData,this.itemsData,!0)},js.prototype.renderShapeTransform=function(t,e){(t._opMdf||e.op._mdf||this._isFirstFrame)&&(e.opacity=t.opacity,e.opacity*=e.op.v,e._opMdf=!0)},js.prototype.drawLayer=function(){var t,e,s,i,a,r,n,o,h,l=this.stylesList.length,p=this.globalData.renderer,f=this.globalData.canvasContext;for(t=0;t<l;t+=1)if(("st"!==(o=(h=this.stylesList[t]).type)&&"gs"!==o||0!==h.wi)&&h.data._shouldRender&&0!==h.coOp&&0!==this.globalData.currentGlobalAlpha){for(p.save(),r=h.elements,"st"===o||"gs"===o?(p.ctxStrokeStyle("st"===o?h.co:h.grd),p.ctxLineWidth(h.wi),p.ctxLineCap(h.lc),p.ctxLineJoin(h.lj),p.ctxMiterLimit(h.ml||0)):p.ctxFillStyle("fl"===o?h.co:h.grd),p.ctxOpacity(h.coOp),"st"!==o&&"gs"!==o&&f.beginPath(),p.ctxTransform(h.preTransforms.finalTransform.props),s=r.length,e=0;e<s;e+=1){for("st"!==o&&"gs"!==o||(f.beginPath(),h.da&&(f.setLineDash(h.da),f.lineDashOffset=h.do)),a=(n=r[e].trNodes).length,i=0;i<a;i+=1)"m"===n[i].t?f.moveTo(n[i].p[0],n[i].p[1]):"c"===n[i].t?f.bezierCurveTo(n[i].pts[0],n[i].pts[1],n[i].pts[2],n[i].pts[3],n[i].pts[4],n[i].pts[5]):f.closePath();"st"!==o&&"gs"!==o||(p.ctxStroke(),h.da&&f.setLineDash(this.dashResetter))}"st"!==o&&"gs"!==o&&this.globalData.renderer.ctxFill(h.r),p.restore()}},js.prototype.renderShape=function(t,e,s,i){var a,r;for(r=t,a=e.length-1;a>=0;a-=1)"tr"===e[a].ty?(r=s[a].transform,this.renderShapeTransform(t,r)):"sh"===e[a].ty||"el"===e[a].ty||"rc"===e[a].ty||"sr"===e[a].ty?this.renderPath(e[a],s[a]):"fl"===e[a].ty?this.renderFill(e[a],s[a],r):"st"===e[a].ty?this.renderStroke(e[a],s[a],r):"gf"===e[a].ty||"gs"===e[a].ty?this.renderGradientFill(e[a],s[a],r):"gr"===e[a].ty?this.renderShape(r,e[a].it,s[a].it):e[a].ty;i&&this.drawLayer()},js.prototype.renderStyledShape=function(t,e){if(this._isFirstFrame||e._mdf||t.transforms._mdf){var s,i,a,r=t.trNodes,n=e.paths,o=n._length;r.length=0;var h=t.transforms.finalTransform;for(a=0;a<o;a+=1){var l=n.shapes[a];if(l&&l.v){for(i=l._length,s=1;s<i;s+=1)1===s&&r.push({t:"m",p:h.applyToPointArray(l.v[0][0],l.v[0][1],0)}),r.push({t:"c",pts:h.applyToTriplePoints(l.o[s-1],l.i[s],l.v[s])});1===i&&r.push({t:"m",p:h.applyToPointArray(l.v[0][0],l.v[0][1],0)}),l.c&&i&&(r.push({t:"c",pts:h.applyToTriplePoints(l.o[s-1],l.i[0],l.v[0])}),r.push({t:"z"}))}}t.trNodes=r}},js.prototype.renderPath=function(t,e){if(!0!==t.hd&&t._shouldRender){var s,i=e.styledShapes.length;for(s=0;s<i;s+=1)this.renderStyledShape(e.styledShapes[s],e.sh)}},js.prototype.renderFill=function(t,e,s){var i=e.style;(e.c._mdf||this._isFirstFrame)&&(i.co="rgb("+v(e.c.v[0])+","+v(e.c.v[1])+","+v(e.c.v[2])+")"),(e.o._mdf||s._opMdf||this._isFirstFrame)&&(i.coOp=e.o.v*s.opacity)},js.prototype.renderGradientFill=function(t,e,s){var i,a=e.style;if(!a.grd||e.g._mdf||e.s._mdf||e.e._mdf||1!==t.t&&(e.h._mdf||e.a._mdf)){var r,n=this.globalData.canvasContext,o=e.s.v,h=e.e.v;if(1===t.t)i=n.createLinearGradient(o[0],o[1],h[0],h[1]);else{var l=Math.sqrt(Math.pow(o[0]-h[0],2)+Math.pow(o[1]-h[1],2)),p=Math.atan2(h[1]-o[1],h[0]-o[0]),f=e.h.v;f>=1?f=.99:f<=-1&&(f=-.99);var c=l*f,d=Math.cos(p+e.a.v)*c+o[0],m=Math.sin(p+e.a.v)*c+o[1];i=n.createRadialGradient(d,m,0,o[0],o[1],l)}var u=t.g.p,g=e.g.c,y=1;for(r=0;r<u;r+=1)e.g._hasOpacity&&e.g._collapsable&&(y=e.g.o[2*r+1]),i.addColorStop(g[4*r]/100,"rgba("+g[4*r+1]+","+g[4*r+2]+","+g[4*r+3]+","+y+")");a.grd=i}a.coOp=e.o.v*s.opacity},js.prototype.renderStroke=function(t,e,s){var i=e.style,a=e.d;a&&(a._mdf||this._isFirstFrame)&&(i.da=a.dashArray,i.do=a.dashoffset[0]),(e.c._mdf||this._isFirstFrame)&&(i.co="rgb("+v(e.c.v[0])+","+v(e.c.v[1])+","+v(e.c.v[2])+")"),(e.o._mdf||s._opMdf||this._isFirstFrame)&&(i.coOp=e.o.v*s.opacity),(e.w._mdf||this._isFirstFrame)&&(i.wi=e.w.v)},js.prototype.destroy=function(){this.shapesData=null,this.globalData=null,this.canvasContext=null,this.stylesList.length=0,this.itemsData.length=0},r([ze,je,Ns,Ze,Oe,Se,ks],Ys),Ys.prototype.tHelper=a("canvas").getContext("2d"),Ys.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=l(t.l?t.l.length:0);var e=!1;t.fc?(e=!0,this.values.fill=this.buildColor(t.fc)):this.values.fill="rgba(0,0,0,0)",this.fill=e;var s=!1;t.sc&&(s=!0,this.values.stroke=this.buildColor(t.sc),this.values.sWidth=t.sw);var i,a,r,n,o,h,p,f,c,d,m,u,g=this.globalData.fontManager.getFontByName(t.f),y=t.l,v=this.mHelper;this.stroke=s,this.values.fValue=t.finalSize+"px "+this.globalData.fontManager.getFontByName(t.f).fFamily,a=t.finalText.length;var b=this.data.singleShape,_=.001*t.tr*t.finalSize,C=0,x=0,k=!0,S=0;for(i=0;i<a;i+=1){n=(r=this.globalData.fontManager.getCharData(t.finalText[i],g.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily))&&r.data||{},v.reset(),b&&y[i].n&&(C=-_,x+=t.yOffset,x+=k?1:0,k=!1),c=(p=n.shapes?n.shapes[0].it:[]).length,v.scale(t.finalSize/100,t.finalSize/100),b&&this.applyTextPropertiesToMatrix(t,v,y[i].line,C,x),m=l(c-1);var P=0;for(f=0;f<c;f+=1)if("sh"===p[f].ty){for(h=p[f].ks.k.i.length,d=p[f].ks.k,u=[],o=1;o<h;o+=1)1===o&&u.push(v.applyToX(d.v[0][0],d.v[0][1],0),v.applyToY(d.v[0][0],d.v[0][1],0)),u.push(v.applyToX(d.o[o-1][0],d.o[o-1][1],0),v.applyToY(d.o[o-1][0],d.o[o-1][1],0),v.applyToX(d.i[o][0],d.i[o][1],0),v.applyToY(d.i[o][0],d.i[o][1],0),v.applyToX(d.v[o][0],d.v[o][1],0),v.applyToY(d.v[o][0],d.v[o][1],0));u.push(v.applyToX(d.o[o-1][0],d.o[o-1][1],0),v.applyToY(d.o[o-1][0],d.o[o-1][1],0),v.applyToX(d.i[0][0],d.i[0][1],0),v.applyToY(d.i[0][0],d.i[0][1],0),v.applyToX(d.v[0][0],d.v[0][1],0),v.applyToY(d.v[0][0],d.v[0][1],0)),m[P]=u,P+=1}b&&(C+=y[i].l,C+=_),this.textSpans[S]?this.textSpans[S].elem=m:this.textSpans[S]={elem:m},S+=1}},Ys.prototype.renderInnerContent=function(){var t,e,s,i,a,r;this.validateText(),this.canvasContext.font=this.values.fValue,this.globalData.renderer.ctxLineCap("butt"),this.globalData.renderer.ctxLineJoin("miter"),this.globalData.renderer.ctxMiterLimit(4),this.data.singleShape||this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag);var n,o=this.textAnimator.renderedLetters,h=this.textProperty.currentData.l;e=h.length;var l,p,f=null,c=null,d=null,m=this.globalData.renderer;for(t=0;t<e;t+=1)if(!h[t].n){if((n=o[t])&&(m.save(),m.ctxTransform(n.p),m.ctxOpacity(n.o)),this.fill){for(n&&n.fc?f!==n.fc&&(m.ctxFillStyle(n.fc),f=n.fc):f!==this.values.fill&&(f=this.values.fill,m.ctxFillStyle(this.values.fill)),i=(l=this.textSpans[t].elem).length,this.globalData.canvasContext.beginPath(),s=0;s<i;s+=1)for(r=(p=l[s]).length,this.globalData.canvasContext.moveTo(p[0],p[1]),a=2;a<r;a+=6)this.globalData.canvasContext.bezierCurveTo(p[a],p[a+1],p[a+2],p[a+3],p[a+4],p[a+5]);this.globalData.canvasContext.closePath(),m.ctxFill()}if(this.stroke){for(n&&n.sw?d!==n.sw&&(d=n.sw,m.ctxLineWidth(n.sw)):d!==this.values.sWidth&&(d=this.values.sWidth,m.ctxLineWidth(this.values.sWidth)),n&&n.sc?c!==n.sc&&(c=n.sc,m.ctxStrokeStyle(n.sc)):c!==this.values.stroke&&(c=this.values.stroke,m.ctxStrokeStyle(this.values.stroke)),i=(l=this.textSpans[t].elem).length,this.globalData.canvasContext.beginPath(),s=0;s<i;s+=1)for(r=(p=l[s]).length,this.globalData.canvasContext.moveTo(p[0],p[1]),a=2;a<r;a+=6)this.globalData.canvasContext.bezierCurveTo(p[a],p[a+1],p[a+2],p[a+3],p[a+4],p[a+5]);this.globalData.canvasContext.closePath(),m.ctxStroke()}n&&this.globalData.renderer.restore()}},r([ze,je,Ns,Ze,Oe,Se],Js),Js.prototype.initElement=ys.prototype.initElement,Js.prototype.prepareFrame=$e.prototype.prepareFrame,Js.prototype.createContent=function(){if(this.img.width&&(this.assetData.w!==this.img.width||this.assetData.h!==this.img.height)){var t=a("canvas");t.width=this.assetData.w,t.height=this.assetData.h;var e,s,i=t.getContext("2d"),r=this.img.width,n=this.img.height,o=r/n,h=this.assetData.w/this.assetData.h,l=this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio;o>h&&"xMidYMid slice"===l||o<h&&"xMidYMid slice"!==l?e=(s=n)*h:s=(e=r)/h,i.drawImage(this.img,(r-e)/2,(n-s)/2,e,s,0,0,this.assetData.w,this.assetData.h),this.img=t}},Js.prototype.renderInnerContent=function(){this.canvasContext.drawImage(this.img,0,0)},Js.prototype.destroy=function(){this.img=null},r([ze,je,Ns,Ze,Oe,Se],Hs),Hs.prototype.initElement=ys.prototype.initElement,Hs.prototype.prepareFrame=$e.prototype.prepareFrame,Hs.prototype.renderInnerContent=function(){this.globalData.renderer.ctxFillStyle(this.data.sc),this.globalData.renderer.ctxFillRect(0,0,this.data.sw,this.data.sh)},r([Be],Gs),Gs.prototype.createShape=function(t){return new js(t,this.globalData,this)},Gs.prototype.createText=function(t){return new Ys(t,this.globalData,this)},Gs.prototype.createImage=function(t){return new Js(t,this.globalData,this)},Gs.prototype.createSolid=function(t){return new Hs(t,this.globalData,this)},Gs.prototype.createNull=Fs.prototype.createNull,Gs.prototype.ctxTransform=function(t){1===t[0]&&0===t[1]&&0===t[4]&&1===t[5]&&0===t[12]&&0===t[13]||this.canvasContext.transform(t[0],t[1],t[4],t[5],t[12],t[13])},Gs.prototype.ctxOpacity=function(t){this.canvasContext.globalAlpha*=t<0?0:t},Gs.prototype.ctxFillStyle=function(t){this.canvasContext.fillStyle=t},Gs.prototype.ctxStrokeStyle=function(t){this.canvasContext.strokeStyle=t},Gs.prototype.ctxLineWidth=function(t){this.canvasContext.lineWidth=t},Gs.prototype.ctxLineCap=function(t){this.canvasContext.lineCap=t},Gs.prototype.ctxLineJoin=function(t){this.canvasContext.lineJoin=t},Gs.prototype.ctxMiterLimit=function(t){this.canvasContext.miterLimit=t},Gs.prototype.ctxFill=function(t){this.canvasContext.fill(t)},Gs.prototype.ctxFillRect=function(t,e,s,i){this.canvasContext.fillRect(t,e,s,i)},Gs.prototype.ctxStroke=function(){this.canvasContext.stroke()},Gs.prototype.reset=function(){this.renderConfig.clearCanvas?this.contextData.reset():this.canvasContext.restore()},Gs.prototype.save=function(){this.canvasContext.save()},Gs.prototype.restore=function(t){this.renderConfig.clearCanvas?(t&&(this.globalData.blendMode="source-over"),this.contextData.restore(t)):this.canvasContext.restore()},Gs.prototype.configAnimation=function(t){if(this.animationItem.wrapper){this.animationItem.container=a("canvas");var e=this.animationItem.container.style;e.width="100%",e.height="100%";var s="0px 0px 0px";e.transformOrigin=s,e.mozTransformOrigin=s,e.webkitTransformOrigin=s,e["-webkit-transform"]=s,e.contentVisibility=this.renderConfig.contentVisibility,this.animationItem.wrapper.appendChild(this.animationItem.container),this.canvasContext=this.animationItem.container.getContext("2d"),this.renderConfig.className&&this.animationItem.container.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.animationItem.container.setAttribute("id",this.renderConfig.id)}else this.canvasContext=this.renderConfig.context;this.contextData.setContext(this.canvasContext),this.data=t,this.layers=t.layers,this.transformCanvas={w:t.w,h:t.h,sx:0,sy:0,tx:0,ty:0},this.setupGlobalData(t,document.body),this.globalData.canvasContext=this.canvasContext,this.globalData.renderer=this,this.globalData.isDashed=!1,this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.globalData.transformCanvas=this.transformCanvas,this.elements=l(t.layers.length),this.updateContainerSize()},Gs.prototype.updateContainerSize=function(t,e){var s,i,a,r;if(this.reset(),t?(s=t,i=e,this.canvasContext.canvas.width=s,this.canvasContext.canvas.height=i):(this.animationItem.wrapper&&this.animationItem.container?(s=this.animationItem.wrapper.offsetWidth,i=this.animationItem.wrapper.offsetHeight):(s=this.canvasContext.canvas.width,i=this.canvasContext.canvas.height),this.canvasContext.canvas.width=s*this.renderConfig.dpr,this.canvasContext.canvas.height=i*this.renderConfig.dpr),-1!==this.renderConfig.preserveAspectRatio.indexOf("meet")||-1!==this.renderConfig.preserveAspectRatio.indexOf("slice")){var n=this.renderConfig.preserveAspectRatio.split(" "),o=n[1]||"meet",h=n[0]||"xMidYMid",l=h.substr(0,4),p=h.substr(4);a=s/i,(r=this.transformCanvas.w/this.transformCanvas.h)>a&&"meet"===o||r<a&&"slice"===o?(this.transformCanvas.sx=s/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=s/(this.transformCanvas.w/this.renderConfig.dpr)):(this.transformCanvas.sx=i/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.h/this.renderConfig.dpr)),this.transformCanvas.tx="xMid"===l&&(r<a&&"meet"===o||r>a&&"slice"===o)?(s-this.transformCanvas.w*(i/this.transformCanvas.h))/2*this.renderConfig.dpr:"xMax"===l&&(r<a&&"meet"===o||r>a&&"slice"===o)?(s-this.transformCanvas.w*(i/this.transformCanvas.h))*this.renderConfig.dpr:0,this.transformCanvas.ty="YMid"===p&&(r>a&&"meet"===o||r<a&&"slice"===o)?(i-this.transformCanvas.h*(s/this.transformCanvas.w))/2*this.renderConfig.dpr:"YMax"===p&&(r>a&&"meet"===o||r<a&&"slice"===o)?(i-this.transformCanvas.h*(s/this.transformCanvas.w))*this.renderConfig.dpr:0}else"none"===this.renderConfig.preserveAspectRatio?(this.transformCanvas.sx=s/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.tx=0,this.transformCanvas.ty=0):(this.transformCanvas.sx=this.renderConfig.dpr,this.transformCanvas.sy=this.renderConfig.dpr,this.transformCanvas.tx=0,this.transformCanvas.ty=0);this.transformCanvas.props=[this.transformCanvas.sx,0,0,0,0,this.transformCanvas.sy,0,0,0,0,1,0,this.transformCanvas.tx,this.transformCanvas.ty,0,1],this.ctxTransform(this.transformCanvas.props),this.canvasContext.beginPath(),this.canvasContext.rect(0,0,this.transformCanvas.w,this.transformCanvas.h),this.canvasContext.closePath(),this.canvasContext.clip(),this.renderFrame(this.renderedFrame,!0)},Gs.prototype.destroy=function(){var t;for(this.renderConfig.clearCanvas&&this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),t=(this.layers?this.layers.length:0)-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.globalData.canvasContext=null,this.animationItem.container=null,this.destroyed=!0},Gs.prototype.renderFrame=function(t,e){if((this.renderedFrame!==t||!0!==this.renderConfig.clearCanvas||e)&&!this.destroyed&&-1!==t){var s;this.renderedFrame=t,this.globalData.frameNum=t-this.animationItem._isFirstFrame,this.globalData.frameId+=1,this.globalData._mdf=!this.renderConfig.clearCanvas||e,this.globalData.projectInterface.currentFrame=t;var i=this.layers.length;for(this.completeLayers||this.checkLayers(t),s=i-1;s>=0;s-=1)(this.completeLayers||this.elements[s])&&this.elements[s].prepareFrame(t-this.layers[s].st);if(this.globalData._mdf){for(!0===this.renderConfig.clearCanvas?this.canvasContext.clearRect(0,0,this.transformCanvas.w,this.transformCanvas.h):this.save(),s=i-1;s>=0;s-=1)(this.completeLayers||this.elements[s])&&this.elements[s].renderFrame();!0!==this.renderConfig.clearCanvas&&this.restore()}}},Gs.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){var s=this.createItem(this.layers[t],this,this.globalData);e[t]=s,s.initExpressions()}},Gs.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){this.pendingElements.pop().checkParenting()}},Gs.prototype.hide=function(){this.animationItem.container.style.display="none"},Gs.prototype.show=function(){this.animationItem.container.style.display="block"},Ks.prototype.duplicate=function(){var t=2*this._length,e=0;for(e=this._length;e<t;e+=1)this.stack[e]=new Xs;this._length=t},Ks.prototype.reset=function(){this.cArrPos=0,this.cTr.reset(),this.stack[this.cArrPos].opacity=1},Ks.prototype.restore=function(t){this.cArrPos-=1;var e,s=this.stack[this.cArrPos],i=s.transform,a=this.cTr.props;for(e=0;e<16;e+=1)a[e]=i[e];if(t){this.nativeContext.restore();var r=this.stack[this.cArrPos+1];this.appliedFillStyle=r.fillStyle,this.appliedStrokeStyle=r.strokeStyle,this.appliedLineWidth=r.lineWidth,this.appliedLineCap=r.lineCap,this.appliedLineJoin=r.lineJoin,this.appliedMiterLimit=r.miterLimit}this.nativeContext.setTransform(i[0],i[1],i[4],i[5],i[12],i[13]),(t||-1!==s.opacity&&this.currentOpacity!==s.opacity)&&(this.nativeContext.globalAlpha=s.opacity,this.currentOpacity=s.opacity),this.currentFillStyle=s.fillStyle,this.currentStrokeStyle=s.strokeStyle,this.currentLineWidth=s.lineWidth,this.currentLineCap=s.lineCap,this.currentLineJoin=s.lineJoin,this.currentMiterLimit=s.miterLimit},Ks.prototype.save=function(t){t&&this.nativeContext.save();var e=this.cTr.props;this._length<=this.cArrPos&&this.duplicate();var s,i=this.stack[this.cArrPos];for(s=0;s<16;s+=1)i.transform[s]=e[s];this.cArrPos+=1;var a=this.stack[this.cArrPos];a.opacity=i.opacity,a.fillStyle=i.fillStyle,a.strokeStyle=i.strokeStyle,a.lineWidth=i.lineWidth,a.lineCap=i.lineCap,a.lineJoin=i.lineJoin,a.miterLimit=i.miterLimit},Ks.prototype.setOpacity=function(t){this.stack[this.cArrPos].opacity=t},Ks.prototype.setContext=function(t){this.nativeContext=t},Ks.prototype.fillStyle=function(t){this.stack[this.cArrPos].fillStyle!==t&&(this.currentFillStyle=t,this.stack[this.cArrPos].fillStyle=t)},Ks.prototype.strokeStyle=function(t){this.stack[this.cArrPos].strokeStyle!==t&&(this.currentStrokeStyle=t,this.stack[this.cArrPos].strokeStyle=t)},Ks.prototype.lineWidth=function(t){this.stack[this.cArrPos].lineWidth!==t&&(this.currentLineWidth=t,this.stack[this.cArrPos].lineWidth=t)},Ks.prototype.lineCap=function(t){this.stack[this.cArrPos].lineCap!==t&&(this.currentLineCap=t,this.stack[this.cArrPos].lineCap=t)},Ks.prototype.lineJoin=function(t){this.stack[this.cArrPos].lineJoin!==t&&(this.currentLineJoin=t,this.stack[this.cArrPos].lineJoin=t)},Ks.prototype.miterLimit=function(t){this.stack[this.cArrPos].miterLimit!==t&&(this.currentMiterLimit=t,this.stack[this.cArrPos].miterLimit=t)},Ks.prototype.transform=function(t){this.transformMat.cloneFromProps(t);var e=this.cTr;this.transformMat.multiply(e),e.cloneFromProps(this.transformMat.props);var s=e.props;this.nativeContext.setTransform(s[0],s[1],s[4],s[5],s[12],s[13])},Ks.prototype.opacity=function(t){var e=this.stack[this.cArrPos].opacity;e*=t<0?0:t,this.stack[this.cArrPos].opacity!==e&&(this.currentOpacity!==t&&(this.nativeContext.globalAlpha=t,this.currentOpacity=t),this.stack[this.cArrPos].opacity=e)},Ks.prototype.fill=function(t){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fill(t)},Ks.prototype.fillRect=function(t,e,s,i){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fillRect(t,e,s,i)},Ks.prototype.stroke=function(){this.appliedStrokeStyle!==this.currentStrokeStyle&&(this.appliedStrokeStyle=this.currentStrokeStyle,this.nativeContext.strokeStyle=this.appliedStrokeStyle),this.appliedLineWidth!==this.currentLineWidth&&(this.appliedLineWidth=this.currentLineWidth,this.nativeContext.lineWidth=this.appliedLineWidth),this.appliedLineCap!==this.currentLineCap&&(this.appliedLineCap=this.currentLineCap,this.nativeContext.lineCap=this.appliedLineCap),this.appliedLineJoin!==this.currentLineJoin&&(this.appliedLineJoin=this.currentLineJoin,this.nativeContext.lineJoin=this.appliedLineJoin),this.appliedMiterLimit!==this.currentMiterLimit&&(this.appliedMiterLimit=this.currentMiterLimit,this.nativeContext.miterLimit=this.appliedMiterLimit),this.nativeContext.stroke()},r([Gs,Ms,Ns],Us),Us.prototype.renderInnerContent=function(){var t,e=this.canvasContext;for(e.beginPath(),e.moveTo(0,0),e.lineTo(this.data.w,0),e.lineTo(this.data.w,this.data.h),e.lineTo(0,this.data.h),e.lineTo(0,0),e.clip(),t=this.layers.length-1;t>=0;t-=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},Us.prototype.destroy=function(){var t;for(t=this.layers.length-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy();this.layers=null,this.elements=null},Us.prototype.createComp=function(t){return new Us(t,this.globalData,this)},r([Gs],Zs),Zs.prototype.createComp=function(t){return new Us(t,this.globalData,this)},qs=Zs,U["canvas"]=qs,qt.registerModifier("tm",Wt),qt.registerModifier("pb",jt),qt.registerModifier("rp",Jt),qt.registerModifier("rd",Ht),qt.registerModifier("zz",le),qt.registerModifier("op",_e),Lt}));
