{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"ty": {"title": "Type", "description": "Type of layer: Text.", "type": "number", "value": 5}, "ks": {"title": "Transform", "description": "Transform properties", "$ref": "#/helpers/transform", "type": "object"}, "ao": {"title": "Auto-Orient", "description": "Auto-Orient along path AE property.", "$ref": "#/helpers/boolean", "type": "number", "default": 0}, "bm": {"title": "Blend Mode", "description": "Blend Mode", "$ref": "#/helpers/blendMode", "type": "number", "default": 0}, "ddd": {"title": "3d Layer", "description": "3d layer flag", "$ref": "#/helpers/boolean", "type": "number", "default": 0}, "ind": {"title": "Index", "description": "Layer index in AE. Used for parenting and expressions.", "type": "number"}, "cl": {"title": "Class", "description": "Parsed layer name used as html class on SVG/HTML renderer", "type": "string"}, "ln": {"title": "layer HTML ID", "description": "Parsed layer name used as html id on SVG/HTML renderer", "type": "string"}, "ip": {"title": "In Point", "description": "In Point of layer. Sets the initial frame of the layer.", "type": "number"}, "op": {"title": "Out Point", "description": "Out Point of layer. Sets the final frame of the layer.", "type": "number"}, "st": {"title": "Start Time", "description": "Start Time of layer. Sets the start time of the layer.", "type": "number"}, "nm": {"title": "Name", "description": "After Effects Layer Name. Used for expressions.", "type": "number"}, "hasMask": {"title": "Has Masks", "description": "Boolean when layer has a mask. Will be deprecated in favor of checking masksProperties.", "type": "number"}, "masksProperties": {"title": "Masks Properties", "description": "List of Masks", "items": {"$ref": "#/helpers/mask", "type": "object"}, "type": "array"}, "ef": {"title": "Effects", "description": "Auto-Orient along path AE property.", "$ref": "#/helpers/boolean", "type": "number"}, "sr": {"title": "<PERSON><PERSON><PERSON>", "description": "Layer Time Stretching", "type": "number", "default": 1}, "parent": {"title": "Parent", "description": "Layer Parent. Uses ind of parent.", "type": "number"}, "t": {"title": "Text Data", "description": "Text Data", "properties": [{"title": "Animators", "description": "Text animators", "items": {"properties": [{"title": "Animated Properties", "description": "Text animator animated properties", "properties": [{"title": "Position", "description": "Text animator Position", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "type": "object"}, {"title": "Anchor Point", "description": "Text animator <PERSON><PERSON>", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "type": "object"}, {"title": "Scale", "description": "Text animator Scale", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "type": "object"}, {"title": "Skew", "description": "Text animator Skew", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, {"title": "Skew Axis", "description": "Text animator S<PERSON>w <PERSON>", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, {"title": "Rotation", "description": "Text animator Rotation", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, {"title": "Opacity", "description": "Text animator Opacity", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, {"title": "Stroke Width", "description": "Text animator <PERSON><PERSON>", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, {"title": "Stroke Color", "description": "Text animator Stroke Color", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "type": "object"}, {"title": "Fill Color", "description": "Text animator Fill Color", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "type": "object"}, {"title": "<PERSON><PERSON>", "description": "Text animator <PERSON><PERSON>", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, {"title": "Fill Saturation", "description": "Text animator <PERSON><PERSON>", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, {"title": "Fill Brightness", "description": "Text animator Fill <PERSON>", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}, {"title": "Tracking", "description": "Text animator Tracking", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}], "type": "object"}, {"title": "Range Selecton", "description": "Animators <PERSON>", "properties": [{"title": "Type", "description": "Selector Type. Expressible, or Normal.", "type": "number"}, {"title": "<PERSON>", "description": "Selector <PERSON>", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "number"}, {"title": "<PERSON>", "description": "Levels <PERSON>ase", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "number"}, {"title": "<PERSON>", "description": "Levels <PERSON> Ease", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "number"}, {"title": "Randomize", "description": "Selector Randomize Order", "$ref": "#/helpers/boolean", "type": "number"}, {"title": "<PERSON><PERSON><PERSON>", "description": "Selector <PERSON>", "$ref": "#/helpers/textShape", "type": "number"}, {"title": "Based On", "description": "Selector Based On", "$ref": "#/helpers/textBased", "type": "number"}, {"title": "Range Units", "description": "Selector Range Units. Percentage or Index.", "type": "number"}, {"title": "Start", "description": "Selector Start", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "number"}, {"title": "End", "description": "Selector End", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "number"}, {"title": "Offset", "description": "Selector Offset", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "number"}], "type": "object"}], "type": "object"}, "type": "array"}, {"title": "More Options", "description": "Text More Options", "properties": [{"title": "Anchor Point Grouping", "description": "Text Anchor Point Grouping", "$ref": "#/helpers/textGrouping", "type": "number"}, {"title": "Grouping Alignment", "description": "Text Grouping Alignment", "oneOf": [{"$ref": "#/properties/multiDimensional"}, {"$ref": "#/properties/multiDimensionalKeyframed"}], "type": "number"}], "type": "object"}, {"title": "Text Path", "description": "Text Path", "type": "number"}, {"title": "Document", "description": "Text Document Data", "properties": [{"title": "Keyframes", "description": "Text Document Data Keyframes", "items": {"oneOf": [{"properties": [{"title": "Time", "description": "Keyframe Time", "type": "number"}, {"title": "Text Properties", "description": "Text Properties", "type": "object", "properties": [{"title": "Font", "description": "Text Font", "type": "string"}, {"title": "Font Color", "description": "Text Font Color", "type": "array"}, {"title": "Justificaiton", "description": "Text Justification", "type": "string"}, {"title": "Line Height", "description": "Text Line Height", "type": "number"}, {"title": "Size", "description": "Text Font Size", "type": "number"}, {"title": "Text", "description": "Text String Value", "type": "string"}, {"title": "Tracking", "description": "Text Tracking", "type": "number"}]}]}], "type": "object"}, "type": "array"}], "type": "object"}], "type": "object"}}}