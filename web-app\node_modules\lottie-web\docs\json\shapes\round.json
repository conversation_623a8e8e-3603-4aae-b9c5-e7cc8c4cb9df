{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"mn": {"title": "Match Name", "description": "After Effect's Match Name. Used for expressions.", "type": "string"}, "nm": {"title": "Name", "description": "After Effect's Name. Used for expressions.", "type": "string"}, "ty": {"title": "Type", "description": "Shape content type.", "type": "string", "const": "rd"}, "r": {"title": "<PERSON><PERSON>", "description": "Rounded Corner Radius", "oneOf": [{"$ref": "#/properties/value"}, {"$ref": "#/properties/valueKeyframed"}], "type": "object"}}}