import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Toaster } from 'react-hot-toast'
import './App.css'

// Import components with error boundaries
const Navbar = React.lazy(() => import('./components/Navbar'))
const Landing = React.lazy(() => import('./pages/Landing'))
const Login = React.lazy(() => import('./pages/Login'))
const Register = React.lazy(() => import('./pages/Register'))
const Home = React.lazy(() => import('./pages/Home'))
const Horoscope = React.lazy(() => import('./pages/Horoscope'))
const DailyInstructions = React.lazy(() => import('./pages/DailyInstructions'))
const FutureReadings = React.lazy(() => import('./pages/FutureReadings'))
const Profile = React.lazy(() => import('./pages/Profile'))

function App() {
  console.log('App component rendering...')

  return (
    <Router>
      <div className="min-h-screen bg-mythology-gradient text-white">
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'rgba(30, 41, 59, 0.95)',
              color: '#fff',
              border: '1px solid rgba(251, 191, 36, 0.3)',
              borderRadius: '12px',
              backdropFilter: 'blur(10px)',
            },
            success: {
              iconTheme: {
                primary: '#fbbf24',
                secondary: '#1e293b',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#1e293b',
              },
            },
          }}
        />

        <React.Suspense fallback={
          <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
            <div className="text-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="text-6xl text-yellow-400 mb-4"
              >
                👑
              </motion.div>
              <p className="text-yellow-400 text-lg font-medium">Loading Kubera...</p>
            </div>
          </div>
        }>
          <Routes>
            {/* Landing page without navbar */}
            <Route path="/" element={<Landing />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* App pages with navbar */}
            <Route path="/home" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <Home />
                </motion.main>
              </>
            } />
            <Route path="/horoscope" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <Horoscope />
                </motion.main>
              </>
            } />
            <Route path="/daily-instructions" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <DailyInstructions />
                </motion.main>
              </>
            } />
            <Route path="/future-readings" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <FutureReadings />
                </motion.main>
              </>
            } />
            <Route path="/profile" element={
              <>
                <Navbar />
                <motion.main
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="w-full"
                >
                  <Profile />
                </motion.main>
              </>
            } />
          </Routes>
        </React.Suspense>
      </div>
    </Router>
  )
}

export default App
