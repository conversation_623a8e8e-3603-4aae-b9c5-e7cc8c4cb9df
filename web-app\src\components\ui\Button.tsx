import React from 'react'
import { motion } from 'framer-motion'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '../../lib/utils'

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-semibold transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden group",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-yellow-400 to-yellow-600 text-slate-900 shadow-lg hover:shadow-yellow-400/25 hover:from-yellow-300 hover:to-yellow-500",
        destructive: "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:shadow-red-500/25 hover:from-red-400 hover:to-red-500",
        outline: "border-2 border-yellow-400 text-yellow-400 bg-transparent hover:bg-yellow-400 hover:text-slate-900 shadow-lg hover:shadow-yellow-400/25",
        secondary: "bg-gradient-to-r from-slate-700 to-slate-800 text-white shadow-lg hover:shadow-slate-700/25 hover:from-slate-600 hover:to-slate-700",
        ghost: "text-slate-300 hover:text-yellow-400 hover:bg-yellow-400/10",
        link: "text-yellow-400 underline-offset-4 hover:underline hover:text-yellow-300",
        premium: "bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 text-white shadow-lg hover:shadow-purple-500/25 hover:scale-105",
        luxury: "bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600 text-slate-900 shadow-xl hover:shadow-amber-400/30 hover:scale-105 border border-amber-300/50",
        glass: "bg-white/10 backdrop-blur-md border border-white/20 text-white shadow-lg hover:bg-white/20 hover:shadow-white/10",
      },
      size: {
        default: "h-12 px-6 py-3",
        sm: "h-9 px-4 py-2 text-xs",
        lg: "h-14 px-8 py-4 text-base",
        xl: "h-16 px-10 py-5 text-lg",
        icon: "h-12 w-12",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, loading, leftIcon, rightIcon, children, disabled, ...props }, ref) => {
    const isDisabled = disabled || loading

    return (
      <motion.button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={isDisabled}
        whileHover={{ scale: variant === 'ghost' || variant === 'link' ? 1 : 1.02 }}
        whileTap={{ scale: variant === 'ghost' || variant === 'link' ? 1 : 0.98 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
        {...props}
      >
        {/* Shimmer effect for premium variants */}
        {(variant === 'premium' || variant === 'luxury') && (
          <div className="absolute inset-0 -top-[1px] bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer" />
        )}
        
        {/* Loading spinner */}
        {loading && (
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        )}
        
        {/* Left icon */}
        {leftIcon && !loading && (
          <span className="flex-shrink-0">
            {leftIcon}
          </span>
        )}
        
        {/* Button content */}
        {children && (
          <span className={cn(
            "transition-all duration-300",
            loading && "opacity-70"
          )}>
            {children}
          </span>
        )}
        
        {/* Right icon */}
        {rightIcon && !loading && (
          <span className="flex-shrink-0 transition-transform duration-300 group-hover:translate-x-0.5">
            {rightIcon}
          </span>
        )}
        
        {/* Ripple effect */}
        <div className="absolute inset-0 rounded-xl overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
        </div>
      </motion.button>
    )
  }
)

Button.displayName = "Button"

export { Button, buttonVariants }
