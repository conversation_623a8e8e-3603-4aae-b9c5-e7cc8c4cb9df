import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '../../lib/utils'

interface LoadingProps {
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'crown' | 'mystical'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'default' | 'premium' | 'luxury'
  text?: string
  fullScreen?: boolean
  className?: string
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-8 h-8',
  lg: 'w-12 h-12',
  xl: 'w-16 h-16',
}

const colorClasses = {
  default: 'text-yellow-400',
  premium: 'text-purple-400',
  luxury: 'text-amber-400',
}

export const Loading: React.FC<LoadingProps> = ({
  variant = 'spinner',
  size = 'md',
  color = 'default',
  text,
  fullScreen = false,
  className,
}) => {
  const renderSpinner = () => {
    switch (variant) {
      case 'spinner':
        return (
          <motion.div
            className={cn(
              'border-2 border-current border-t-transparent rounded-full',
              sizeClasses[size],
              colorClasses[color]
            )}
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        )

      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className={cn(
                  'rounded-full',
                  size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-2 h-2' : size === 'lg' ? 'w-3 h-3' : 'w-4 h-4',
                  colorClasses[color] === 'text-yellow-400' ? 'bg-yellow-400' :
                  colorClasses[color] === 'text-purple-400' ? 'bg-purple-400' : 'bg-amber-400'
                )}
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>
        )

      case 'pulse':
        return (
          <motion.div
            className={cn(
              'rounded-full',
              sizeClasses[size],
              colorClasses[color] === 'text-yellow-400' ? 'bg-yellow-400' :
              colorClasses[color] === 'text-purple-400' ? 'bg-purple-400' : 'bg-amber-400'
            )}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        )

      case 'bars':
        return (
          <div className="flex space-x-1 items-end">
            {[0, 1, 2, 3].map((i) => (
              <motion.div
                key={i}
                className={cn(
                  'rounded-sm',
                  size === 'sm' ? 'w-1' : size === 'md' ? 'w-1.5' : size === 'lg' ? 'w-2' : 'w-3',
                  colorClasses[color] === 'text-yellow-400' ? 'bg-yellow-400' :
                  colorClasses[color] === 'text-purple-400' ? 'bg-purple-400' : 'bg-amber-400'
                )}
                animate={{
                  height: [
                    size === 'sm' ? 8 : size === 'md' ? 16 : size === 'lg' ? 24 : 32,
                    size === 'sm' ? 16 : size === 'md' ? 32 : size === 'lg' ? 48 : 64,
                    size === 'sm' ? 8 : size === 'md' ? 16 : size === 'lg' ? 24 : 32,
                  ],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.1,
                }}
              />
            ))}
          </div>
        )

      case 'crown':
        return (
          <motion.div
            className={cn(
              'text-6xl',
              colorClasses[color]
            )}
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            👑
          </motion.div>
        )

      case 'mystical':
        return (
          <div className="relative">
            <motion.div
              className={cn(
                'absolute inset-0 rounded-full border-2 border-dashed',
                sizeClasses[size],
                colorClasses[color]
              )}
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            />
            <motion.div
              className={cn(
                'rounded-full border-2 border-solid',
                sizeClasses[size],
                colorClasses[color]
              )}
              animate={{ rotate: -360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            />
            <motion.div
              className={cn(
                'absolute inset-2 rounded-full',
                colorClasses[color] === 'text-yellow-400' ? 'bg-yellow-400' :
                colorClasses[color] === 'text-purple-400' ? 'bg-purple-400' : 'bg-amber-400'
              )}
              animate={{
                scale: [0.8, 1, 0.8],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          </div>
        )

      default:
        return null
    }
  }

  const content = (
    <div className={cn(
      'flex flex-col items-center justify-center space-y-4',
      className
    )}>
      {renderSpinner()}
      {text && (
        <motion.p
          className={cn(
            'text-sm font-medium',
            colorClasses[color]
          )}
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        >
          {text}
        </motion.p>
      )}
    </div>
  )

  if (fullScreen) {
    return (
      <motion.div
        className="fixed inset-0 bg-slate-900/80 backdrop-blur-sm flex items-center justify-center z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {content}
      </motion.div>
    )
  }

  return content
}

// Preset loading components
export const PageLoading: React.FC<{ text?: string }> = ({ text = "Loading..." }) => (
  <Loading variant="crown" size="lg" color="luxury" text={text} fullScreen />
)

export const ButtonLoading: React.FC = () => (
  <Loading variant="spinner" size="sm" />
)

export const CardLoading: React.FC<{ text?: string }> = ({ text }) => (
  <Loading variant="mystical" size="md" color="premium" text={text} />
)

export default Loading
