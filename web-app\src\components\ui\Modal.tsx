import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Dialog } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { cn } from '../../lib/utils'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  description?: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  variant?: 'default' | 'premium' | 'glass' | 'luxury'
  showCloseButton?: boolean
  closeOnOverlayClick?: boolean
  className?: string
}

const sizeClasses = {
  sm: 'max-w-md',
  md: 'max-w-lg',
  lg: 'max-w-2xl',
  xl: 'max-w-4xl',
  full: 'max-w-7xl',
}

const variantClasses = {
  default: 'bg-slate-800/95 border-slate-700/50',
  premium: 'bg-gradient-to-br from-slate-800/95 to-slate-900/95 border-purple-500/30',
  glass: 'bg-white/10 border-white/20 backdrop-blur-xl',
  luxury: 'bg-gradient-to-br from-amber-50/10 to-yellow-50/10 border-amber-400/30',
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  size = 'md',
  variant = 'default',
  showCloseButton = true,
  closeOnOverlayClick = true,
  className,
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog
          as={motion.div}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          open={isOpen}
          onClose={closeOnOverlayClick ? onClose : () => {}}
          className="relative z-50"
        >
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            aria-hidden="true"
          />

          {/* Full-screen container */}
          <div className="fixed inset-0 flex items-center justify-center p-4">
            <Dialog.Panel
              as={motion.div}
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ 
                type: "spring", 
                stiffness: 300, 
                damping: 30,
                duration: 0.4 
              }}
              className={cn(
                'w-full rounded-2xl border shadow-2xl relative overflow-hidden',
                sizeClasses[size],
                variantClasses[variant],
                className
              )}
            >
              {/* Shimmer effect for premium variants */}
              {(variant === 'premium' || variant === 'luxury') && (
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-shimmer" />
              )}

              {/* Header */}
              {(title || showCloseButton) && (
                <div className="flex items-center justify-between p-6 border-b border-slate-700/50">
                  <div>
                    {title && (
                      <Dialog.Title className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-white bg-clip-text text-transparent">
                        {title}
                      </Dialog.Title>
                    )}
                    {description && (
                      <Dialog.Description className="mt-1 text-sm text-slate-400">
                        {description}
                      </Dialog.Description>
                    )}
                  </div>
                  
                  {showCloseButton && (
                    <motion.button
                      onClick={onClose}
                      className="p-2 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700/50 transition-colors duration-200"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </motion.button>
                  )}
                </div>
              )}

              {/* Content */}
              <div className="p-6">
                {children}
              </div>
            </Dialog.Panel>
          </div>
        </Dialog>
      )}
    </AnimatePresence>
  )
}

// Preset modal variants for common use cases
export const ConfirmModal: React.FC<{
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'danger'
}> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default'
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title} size="sm">
      <div className="space-y-4">
        <p className="text-slate-300">{message}</p>
        <div className="flex gap-3 justify-end">
          <motion.button
            onClick={onClose}
            className="px-4 py-2 text-slate-400 hover:text-white transition-colors duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {cancelText}
          </motion.button>
          <motion.button
            onClick={() => {
              onConfirm()
              onClose()
            }}
            className={cn(
              "px-6 py-2 rounded-lg font-medium transition-all duration-200",
              variant === 'danger' 
                ? "bg-red-600 hover:bg-red-700 text-white" 
                : "bg-yellow-600 hover:bg-yellow-700 text-slate-900"
            )}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {confirmText}
          </motion.button>
        </div>
      </div>
    </Modal>
  )
}

export default Modal
