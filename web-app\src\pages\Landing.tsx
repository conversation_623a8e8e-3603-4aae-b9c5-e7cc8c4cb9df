import React, { useEffect, useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion, useScroll, useTransform } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import {
  Crown,
  Sparkles,
  Star,
  LogIn,
  UserPlus,
  ArrowRight,
  ChevronDownIcon,
  PlayIcon
} from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Card, CardContent } from '../components/ui/Card'

const Landing: React.FC = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const { scrollY } = useScroll()
  const y1 = useTransform(scrollY, [0, 300], [0, 100])
  const y2 = useTransform(scrollY, [0, 300], [0, -100])

  const [heroRef, heroInView] = useInView({ threshold: 0.3 })
  const [featuresRef, featuresInView] = useInView({ threshold: 0.2 })

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }
    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  const features = [
    {
      icon: Star,
      title: "AI-Powered Readings",
      description: "Advanced artificial intelligence combined with ancient wisdom for personalized insights",
      gradient: "from-purple-600 to-blue-600"
    },
    {
      icon: Crown,
      title: "Divine Guidance",
      description: "Connect with the wisdom of Kubera and unlock your path to prosperity",
      gradient: "from-yellow-400 to-amber-600"
    },
    {
      icon: Sparkles,
      title: "Mystical Experience",
      description: "Immerse yourself in a premium spiritual journey with stunning visuals",
      gradient: "from-pink-500 to-purple-600"
    }
  ]

  return (
    <div className="min-h-screen relative overflow-hidden bg-slate-900">
      {/* Dynamic Background */}
      <div className="fixed inset-0">
        {/* Primary Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-1000"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`,
            transform: `translate(${mousePosition.x * 0.01}px, ${mousePosition.y * 0.01}px) scale(1.1)`
          }}
        />

        {/* Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/90 to-slate-900/95" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

        {/* Animated Gradient Mesh */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-float" />
          <div className="absolute top-0 right-0 w-96 h-96 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl animate-float" style={{ animationDelay: '2s' }} />
          <div className="absolute bottom-0 left-1/2 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-float" style={{ animationDelay: '4s' }} />
        </div>
      </div>

      {/* Advanced Particle System */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              width: Math.random() * 4 + 1,
              height: Math.random() * 4 + 1,
              background: `hsl(${45 + Math.random() * 60}, 70%, 60%)`,
            }}
            animate={{
              y: [0, -100 - Math.random() * 100, 0],
              x: [0, Math.random() * 50 - 25, 0],
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "easeInOut",
            }}
          />
        ))}

        {/* Mystical Orbs */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`orb-${i}`}
            className="absolute w-32 h-32 rounded-full opacity-10"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              background: `radial-gradient(circle, ${i % 2 === 0 ? '#fbbf24' : '#a855f7'} 0%, transparent 70%)`,
            }}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.1, 0.3, 0.1],
              rotate: [0, 360],
            }}
            transition={{
              duration: 10 + Math.random() * 5,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>

      {/* Hero Section */}
      <section ref={heroRef} className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <div className="text-center max-w-6xl mx-auto">
          {/* Premium Logo */}
          <motion.div
            initial={{ scale: 0, rotate: -180, opacity: 0 }}
            animate={heroInView ? { scale: 1, rotate: 0, opacity: 1 } : {}}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="mb-12"
          >
            <div className="relative inline-block">
              <motion.div
                className="w-32 h-32 bg-gradient-to-r from-yellow-400 via-amber-500 to-yellow-600 rounded-full shadow-2xl flex items-center justify-center relative overflow-hidden"
                animate={{
                  boxShadow: [
                    "0 25px 50px -12px rgba(251, 191, 36, 0.25)",
                    "0 25px 50px -12px rgba(251, 191, 36, 0.6)",
                    "0 25px 50px -12px rgba(251, 191, 36, 0.25)"
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Crown size={64} className="text-slate-900 relative z-10" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />
              </motion.div>

              {/* Floating Crown Particles */}
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-4 h-4 text-yellow-400"
                  style={{
                    left: `${50 + Math.cos((i * 60) * Math.PI / 180) * 80}%`,
                    top: `${50 + Math.sin((i * 60) * Math.PI / 180) * 80}%`,
                  }}
                  animate={{
                    rotate: [0, 360],
                    scale: [0.5, 1, 0.5],
                    opacity: [0.3, 1, 0.3],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: i * 0.5,
                  }}
                >
                  ✨
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Premium Title */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 1, delay: 0.5 }}
            className="mb-8"
          >
            <h1 className="text-8xl md:text-9xl lg:text-[12rem] font-bold mb-4 relative">
              <span className="bg-gradient-to-r from-yellow-400 via-amber-300 to-yellow-500 bg-clip-text text-transparent animate-gradient text-glow">
                Kubera
              </span>
              <motion.div
                className="absolute -inset-4 bg-gradient-to-r from-yellow-400/20 via-transparent to-purple-400/20 blur-3xl"
                animate={{ opacity: [0.3, 0.7, 0.3] }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </h1>

            <motion.div
              initial={{ width: 0 }}
              animate={heroInView ? { width: "100%" } : {}}
              transition={{ duration: 1.5, delay: 1 }}
              className="h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto max-w-md"
            />
          </motion.div>

          {/* Premium Subtitle */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 1, delay: 0.8 }}
            className="mb-12"
          >
            <motion.h2
              className="text-3xl md:text-4xl lg:text-5xl font-light mb-6 text-white"
              animate={{
                textShadow: [
                  "0 0 20px rgba(255, 255, 255, 0.5)",
                  "0 0 40px rgba(255, 255, 255, 0.8)",
                  "0 0 20px rgba(255, 255, 255, 0.5)"
                ]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              Unlock Your <span className="text-luxury-gradient font-bold">Divine Destiny</span>
            </motion.h2>

            <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed mb-8">
              Experience the convergence of ancient wisdom and cutting-edge AI technology.
              Discover your path through personalized readings, divine guidance, and mystical insights.
            </p>

            <div className="flex flex-wrap justify-center gap-4 text-sm text-slate-400">
              <span className="flex items-center gap-2">
                <Star size={16} className="text-yellow-400" />
                AI-Powered Insights
              </span>
              <span className="flex items-center gap-2">
                <Crown size={16} className="text-yellow-400" />
                Ancient Wisdom
              </span>
              <span className="flex items-center gap-2">
                <Sparkles size={16} className="text-yellow-400" />
                Premium Experience
              </span>
            </div>
          </motion.div>

          {/* Premium Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 1, delay: 1.2 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
          >
            <Button
              asChild
              variant="luxury"
              size="xl"
              className="min-w-[240px] group"
            >
              <Link to="/login">
                <LogIn size={24} />
                <span>Enter the Realm</span>
                <ArrowRight size={20} className="group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </Button>

            <Button
              asChild
              variant="glass"
              size="xl"
              className="min-w-[240px] group"
            >
              <Link to="/register">
                <UserPlus size={24} />
                <span>Begin Your Journey</span>
                <ArrowRight size={20} className="group-hover:translate-x-1 transition-transform duration-300" />
              </Link>
            </Button>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={heroInView ? { opacity: 1 } : {}}
            transition={{ duration: 1, delay: 1.5 }}
            className="flex flex-col items-center"
          >
            <p className="text-slate-400 text-sm mb-4">Discover More</p>
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="text-yellow-400"
            >
              <ChevronDownIcon size={24} />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Premium Features Section */}
      <section ref={featuresRef} className="relative z-10 py-32 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={featuresInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 1 }}
            className="text-center mb-20"
          >
            <h2 className="text-5xl md:text-6xl font-bold mb-6 text-premium-gradient">
              Divine Features
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Experience the perfect fusion of ancient wisdom and modern technology
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  animate={featuresInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                >
                  <Card
                    variant="premium"
                    className="h-full interactive-hover group"
                    glowEffect
                  >
                    <CardContent className="p-8 text-center">
                      <motion.div
                        className={`w-20 h-20 rounded-2xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-transform duration-300`}
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                      >
                        <Icon size={40} className="text-white" />
                      </motion.div>

                      <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-luxury-gradient transition-colors duration-300">
                        {feature.title}
                      </h3>

                      <p className="text-slate-400 leading-relaxed">
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="relative z-10 py-32 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1 }}
            className="glass-effect rounded-3xl p-12"
          >
            <h3 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Unlock Your <span className="text-luxury-gradient">Divine Potential</span>?
            </h3>
            <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
              Join thousands who have discovered their true path through the wisdom of Kubera
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="luxury" size="xl" asChild>
                <Link to="/register">
                  <PlayIcon size={24} />
                  Start Your Journey
                </Link>
              </Button>

              <Button variant="glass" size="xl" asChild>
                <Link to="/login">
                  <LogIn size={24} />
                  Sign In
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Floating Decorative Elements */}
      <motion.div
        className="fixed top-20 left-20 opacity-20 pointer-events-none"
        style={{ y: y1 }}
      >
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <Star size={40} className="text-yellow-400" />
        </motion.div>
      </motion.div>

      <motion.div
        className="fixed bottom-20 right-20 opacity-20 pointer-events-none"
        style={{ y: y2 }}
      >
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
        >
          <Sparkles size={36} className="text-purple-400" />
        </motion.div>
      </motion.div>
    </div>
  )
}

export default Landing
