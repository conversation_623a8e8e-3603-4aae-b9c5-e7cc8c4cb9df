import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import toast from 'react-hot-toast'
import {
  Crown,
  Mail,
  Lock,
  UserPlus,
  ArrowLeft,
  Sparkles,
  Star,
  User,
  Calendar
} from 'lucide-react'
import { Button } from '../components/ui/Button'
import { Input } from '../components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/Card'

const registerSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  birthDate: z.string().min(1, 'Birth date is required for accurate readings'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type RegisterFormData = z.infer<typeof registerSchema>

const Register: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  })

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      toast.success('Account created successfully! Welcome to Kubera!')

      // Redirect to login page after successful registration
      setTimeout(() => {
        window.location.href = '/login'
      }, 1500)

    } catch (error) {
      toast.error('Registration failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Image with Overlay */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-*************-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/90 to-slate-900/95"></div>
        <div className="absolute inset-0 bg-black/50"></div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-yellow-400 rounded-full opacity-40"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.8, 0.2],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Navigation */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative z-20 p-8"
      >
        <Button variant="ghost" asChild>
          <Link to="/" className="flex items-center gap-2">
            <ArrowLeft size={20} />
            <span>Back to Home</span>
          </Link>
        </Button>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 40, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
          className="w-full max-w-lg"
        >
          <Card variant="premium" className="overflow-hidden">
            <CardHeader className="text-center pb-8">
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="mb-6"
              >
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-yellow-400 to-amber-600 rounded-full shadow-2xl relative">
                  <Crown size={40} className="text-slate-900" />
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer rounded-full" />
                </div>
              </motion.div>

              <CardTitle className="text-4xl mb-2">
                Begin Your Journey
              </CardTitle>
              <CardDescription className="text-lg">
                Create your account to unlock divine wisdom
              </CardDescription>
            </CardHeader>

            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Full Name Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-slate-300">
                    Full Name
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User size={20} className="text-slate-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Enter your full name"
                      className="flex w-full rounded-xl border border-purple-500/50 bg-slate-800/50 text-white px-4 py-3 pl-10 text-sm transition-all duration-300 backdrop-blur-sm focus:border-purple-400 focus:ring-2 focus:ring-purple-400/50 focus:shadow-lg focus:shadow-purple-400/10 focus-visible:outline-none placeholder:text-slate-400"
                      {...register('fullName')}
                    />
                  </div>
                  {errors.fullName && (
                    <p className="text-sm text-red-400">{errors.fullName.message}</p>
                  )}
                </div>

                {/* Email Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-slate-300">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail size={20} className="text-slate-400" />
                    </div>
                    <input
                      type="email"
                      placeholder="Enter your email"
                      className="flex w-full rounded-xl border border-purple-500/50 bg-slate-800/50 text-white px-4 py-3 pl-10 text-sm transition-all duration-300 backdrop-blur-sm focus:border-purple-400 focus:ring-2 focus:ring-purple-400/50 focus:shadow-lg focus:shadow-purple-400/10 focus-visible:outline-none placeholder:text-slate-400"
                      {...register('email')}
                    />
                  </div>
                  {errors.email && (
                    <p className="text-sm text-red-400">{errors.email.message}</p>
                  )}
                </div>

                {/* Birth Date Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-slate-300">
                    Birth Date
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Calendar size={20} className="text-slate-400" />
                    </div>
                    <input
                      type="date"
                      className="flex w-full rounded-xl border border-purple-500/50 bg-slate-800/50 text-white px-4 py-3 pl-10 text-sm transition-all duration-300 backdrop-blur-sm focus:border-purple-400 focus:ring-2 focus:ring-purple-400/50 focus:shadow-lg focus:shadow-purple-400/10 focus-visible:outline-none placeholder:text-slate-400"
                      {...register('birthDate')}
                    />
                  </div>
                  {errors.birthDate && (
                    <p className="text-sm text-red-400">{errors.birthDate.message}</p>
                  )}
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-slate-300">
                    Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock size={20} className="text-slate-400" />
                    </div>
                    <input
                      type="password"
                      placeholder="Create a strong password"
                      className="flex w-full rounded-xl border border-purple-500/50 bg-slate-800/50 text-white px-4 py-3 pl-10 text-sm transition-all duration-300 backdrop-blur-sm focus:border-purple-400 focus:ring-2 focus:ring-purple-400/50 focus:shadow-lg focus:shadow-purple-400/10 focus-visible:outline-none placeholder:text-slate-400"
                      {...register('password')}
                    />
                  </div>
                  {errors.password && (
                    <p className="text-sm text-red-400">{errors.password.message}</p>
                  )}
                </div>

                {/* Confirm Password Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-slate-300">
                    Confirm Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock size={20} className="text-slate-400" />
                    </div>
                    <input
                      type="password"
                      placeholder="Confirm your password"
                      className="flex w-full rounded-xl border border-purple-500/50 bg-slate-800/50 text-white px-4 py-3 pl-10 text-sm transition-all duration-300 backdrop-blur-sm focus:border-purple-400 focus:ring-2 focus:ring-purple-400/50 focus:shadow-lg focus:shadow-purple-400/10 focus-visible:outline-none placeholder:text-slate-400"
                      {...register('confirmPassword')}
                    />
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-sm text-red-400">{errors.confirmPassword.message}</p>
                  )}
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-14 px-8 py-4 text-base bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 text-white shadow-lg hover:shadow-purple-500/25 hover:scale-105 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <UserPlus size={20} />
                  )}
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </button>
              </form>

              {/* Sign In Link */}
              <div className="text-center mt-8 pt-6 border-t border-slate-700/50">
                <p className="text-slate-400">
                  Already have an account?{' '}
                  <Link
                    to="/login"
                    className="text-purple-400 hover:text-purple-300 font-semibold transition-colors duration-300 hover:underline"
                  >
                    Sign in here
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-20 right-20 opacity-20">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <Star size={24} className="text-yellow-400" />
        </motion.div>
      </div>
      
      <div className="absolute bottom-20 left-20 opacity-20">
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
        >
          <Sparkles size={20} className="text-yellow-400" />
        </motion.div>
      </div>
    </div>
  )
}

export default Register
